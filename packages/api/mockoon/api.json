{"uuid": "0389a975-5408-4f1f-bfd4-241135db1d55", "lastMigration": 33, "name": "RL API", "endpointPrefix": "", "latency": 500, "port": 8880, "hostname": "", "folders": [], "routes": [{"uuid": "7d5629be-f812-4530-8bdb-14a8138e7641", "type": "http", "documentation": "", "method": "post", "endpoint": "auth/login", "responses": [{"uuid": "9e3c2d04-3625-4c31-afbf-28de489fad42", "body": "", "latency": 300, "statusCode": 200, "label": "successful response, correct creds", "headers": [{"key": "Set-<PERSON><PERSON>", "value": "refreshToken={{faker 'internet.jwt'}}; Domain=localhost; Secure; HttpOnly"}], "bodyType": "DATABUCKET", "filePath": "", "databucketID": "qsvp", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "email", "value": "<EMAIL>", "invert": false, "operator": "regex"}, {"target": "body", "modifier": "password", "value": "admin", "invert": false, "operator": "regex"}], "rulesOperator": "AND", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}, {"uuid": "99c8c655-a18e-4451-9737-9238c3e9b5ba", "body": "{\n  \"message\": \"Unauthorized, <NAME_EMAIL>:admin\"\n}", "latency": 300, "statusCode": 401, "label": "wrong creds", "headers": [{"key": "Content-Type", "value": "application/json"}], "bodyType": "INLINE", "filePath": "", "databucketID": "qsvp", "sendFileAsBody": false, "rules": [{"target": "body", "modifier": "email", "value": "\\w+", "invert": false, "operator": "regex"}, {"target": "body", "modifier": "password", "value": "\\w+", "invert": false, "operator": "regex"}], "rulesOperator": "AND", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}, {"uuid": "6be7db98-644c-4766-be2c-8fab0991931c", "body": "{\n  \"error\": \"Bad request, missed creds\"\n}", "latency": 0, "statusCode": 400, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "c6821be7-2fc7-4bce-ab0c-47d9be846335", "type": "http", "documentation": "", "method": "post", "endpoint": "auth/token/refresh", "responses": [{"uuid": "6e2c8aaa-69bc-4dbb-9045-610bb11af287", "body": "", "latency": 222, "statusCode": 200, "label": "", "headers": [], "bodyType": "DATABUCKET", "filePath": "", "databucketID": "j3hk", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": true, "fallbackTo404": true, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "bbf88985-f7af-431a-acd0-7da959de39d7", "type": "http", "documentation": "here we imitate random 401 bahavior", "method": "get", "endpoint": "player/btag", "responses": [{"uuid": "87a9e42f-114a-492b-bd0d-30eb57dba6ff", "body": "{}", "latency": 0, "statusCode": 401, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}, {"uuid": "37e056de-1263-4f1a-8681-a4b6e1ed0dd1", "body": "{\n  \"btag\": \"test\"\n}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "INLINE", "filePath": "", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": false, "crudKey": "id", "callbacks": []}], "responseMode": "RANDOM", "streamingMode": null, "streamingInterval": 0}, {"uuid": "27b3b459-fe60-4dc8-8454-d32a2859d23a", "type": "http", "documentation": "", "method": "get", "endpoint": "banners", "responses": [{"uuid": "bc263ae3-a0f4-4081-a5a5-9aec09a3fe92", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "../data/rl/banners.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "97587ec8-9b09-4649-bb0e-0bd8a2335ac1", "type": "http", "documentation": "", "method": "get", "endpoint": "cashback", "responses": [{"uuid": "cc5f0813-0c1d-4fbd-81d3-11daf3af94cf", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "../data/rl/cashback.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}, {"uuid": "63f40de5-9ead-4235-82b5-d6a850ef5ec6", "type": "http", "documentation": "", "method": "get", "endpoint": "promotions", "responses": [{"uuid": "8bdbd62a-1995-4d22-917a-4f6935430c30", "body": "{}", "latency": 0, "statusCode": 200, "label": "", "headers": [], "bodyType": "FILE", "filePath": "../data/rl/promotions.json", "databucketID": "", "sendFileAsBody": false, "rules": [], "rulesOperator": "OR", "disableTemplating": false, "fallbackTo404": false, "default": true, "crudKey": "id", "callbacks": []}], "responseMode": null, "streamingMode": null, "streamingInterval": 0}], "rootChildren": [{"type": "route", "uuid": "7d5629be-f812-4530-8bdb-14a8138e7641"}, {"type": "route", "uuid": "c6821be7-2fc7-4bce-ab0c-47d9be846335"}, {"type": "route", "uuid": "bbf88985-f7af-431a-acd0-7da959de39d7"}, {"type": "route", "uuid": "27b3b459-fe60-4dc8-8454-d32a2859d23a"}, {"type": "route", "uuid": "97587ec8-9b09-4649-bb0e-0bd8a2335ac1"}, {"type": "route", "uuid": "63f40de5-9ead-4235-82b5-d6a850ef5ec6"}], "proxyMode": true, "proxyHost": "https://api-middleware.dev.rhinoent.net", "proxyRemovePrefix": false, "tlsOptions": {"enabled": false, "type": "CERT", "pfxPath": "", "certPath": "", "keyPath": "", "caPath": "", "passphrase": ""}, "cors": true, "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Origin, Accept, Authorization, Content-Length, X-Requested-With, x-correlation-id"}], "proxyReqHeaders": [{"key": "X-Api-Key", "value": "8f031c91-0fd6-4cb0-b479-127db06d065b"}, {"key": "Accept", "value": "application/json;v=3"}], "proxyResHeaders": [{"key": "", "value": ""}], "data": [{"uuid": "682656dd-7ab7-4eb2-bc76-11f6507beff9", "id": "uj4s", "name": "Users", "documentation": "", "value": "[\n  {{#repeat 50}}\n  {\n    \"id\": \"{{faker 'string.uuid'}}\",\n    \"username\": \"{{faker 'internet.username'}}\"\n  }\n  {{/repeat}}\n]"}, {"uuid": "dd98d96c-1a11-4d8a-9fef-514df88e9f10", "id": "qsvp", "name": "auth response", "documentation": "", "value": "{\n    \"status\": \"Success\",\n    \"timestamp\": \"{{now 'yyyy-MM-dd\\'T\\'HH:mm:ss.SSS\\'Z\\''}}\",\n    \"data\": {\n        \"userInfo\": {\n            \"success\": true,\n            \"id\": \"ul0ugh79on6ntrqqmfdcann95p25e8g2\",\n            \"sessionLimit\": 0,\n            \"ecrCategory\": \"play_user\",\n            \"language\": \"en_US\",\n            \"rvpSessionId\": \"44a655d7-304a-4880-b1fb-0a128f50901f_SESSIONKEY\",\n            \"riverplayUrl\": \"wss://push.rhinoent.net\",\n            \"pragmaticSessionId\": \"44a655d7-304a-4880-b1fb-0a128f50901f_SESSIONKEY\",\n            \"pragmaticUrl\": \"wss://push.rhinoent.net\",\n            \"mobileVerificationStatus\": \"NOT_INTIATED\",\n            \"emailVerificationStatus\": \"NOT_INTIATED\",\n            \"twoFactorAuthEnabled\": false,\n            \"user\": {\n                \"id\": \"CAD207461729935065\",\n                \"rvpSessionId\": \"44a655d7-304a-4880-b1fb-0a128f50901f_SESSIONKEY\",\n                \"riverplayUrl\": \"wss://push.rhinoent.net\",\n                \"pragmaticSessionId\": \"44a655d7-304a-4880-b1fb-0a128f50901f_SESSIONKEY\",\n                \"pragmaticUrl\": \"wss://push.rhinoent.net\",\n                \"partnerId\": \"casinodays\"\n            },\n            \"lastLoginTime\": \"1744048381\",\n            \"userJurisdiction\": \"curacao\",\n            \"jurisdictionResponse\": \"\",\n            \"postAuthPopUpDetails\": [],\n            \"policies\": [],\n            \"geo\": {\n                \"countryCode\": \"SK\",\n                \"state\": \"\",\n                \"city\": \"MAIN\"\n            }\n        },\n        \"accessToken\": \"{{faker 'internet.jwt'}}\",\n        \"refreshToken\": \"{{faker 'internet.jwt'}}\"\n    }\n}"}, {"uuid": "b158c55e-d67d-49bc-b9b3-239dd247855c", "id": "j3hk", "name": "refresh token response", "documentation": "", "value": "{\n    \"status\": \"Success\",\n    \"timestamp\": \"{{now 'yyyy-MM-dd\\'T\\'HH:mm:ss.SSS\\'Z\\''}}\",\n    \"data\": {\n        \"accessToken\": \"{{faker 'internet.jwt'}}\",\n        \"refreshToken\": \"{{faker 'internet.jwt'}}\"\n    },\n    \"var\": \"{{getGlobalVar 'counter'}}\"\n}"}], "callbacks": []}