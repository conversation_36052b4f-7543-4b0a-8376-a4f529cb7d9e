type: collection.insomnia.rest/5.0
name: APIs
meta:
  id: wrk_c5019287ddc847fbbc51c16bbcffd940
  created: 1744025108230
  modified: 1746694526101
  description: ""
collection:
  - name: RL api
    meta:
      id: fld_5d5b6d35054c436386db89eed920753e
      created: 1746694314013
      modified: 1746694314013
      sortKey: -1746694314013
      description: ""
    children:
      - url: "{{ _.rlApiBaseUrl }}/auth/login"
        name: login
        meta:
          id: req_2d18b1d5f18d417bb7d661e6788f42b5
          created: 1744025118539
          modified: 1746694793696
          isPrivate: false
          description: ""
          sortKey: -1746694320682
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"email": "<EMAIL>",
            	"password": "admin"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.rlApiBaseUrl }}/auth/token/refresh"
        name: refresh-token
        meta:
          id: req_e0583c5c098a486f891a0e658c1c35dc
          created: 1745822793526
          modified: 1746694783643
          isPrivate: false
          description: ""
          sortKey: -1746694320782
        method: POST
        body:
          mimeType: application/json
          text: ""
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/11.0.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.rlApiBaseUrl }}/player/btag"
        name: btag
        meta:
          id: req_e8cc5bc497b9452abe5c1a53577d26f9
          created: 1745835790445
          modified: 1747722558061
          isPrivate: false
          description: ""
          sortKey: -1746694320882
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.0.1
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.rlApiBaseUrl }}/banners"
        name: banners
        meta:
          id: req_c538efacf45f4737bd8efb7c0af5b350
          created: 1747721261714
          modified: 1747723228130
          isPrivate: false
          description: ""
          sortKey: -1746694320832
        method: GET
        parameters:
          - id: pair_8fb5182690e14eeb8637f0b8f4e8cdb4
            name: iso2
            value: en
            description: ""
            disabled: false
          - id: pair_d42b1c3fa7f5426ebbb691d9bb86bbcc
            name: market
            value: ROW
            description: ""
            disabled: false
          - id: pair_b77d578921124563ba4ba5c39356a002
            name: tags
            value: display:web-only,display:both
            description: ""
            disabled: false
          - id: pair_7f986fa0c1c6425dbe4633fa34be5625
            name: license
            value: curacao
            description: ""
            disabled: false
        headers:
          - name: Accept
            value: application/json;v=1
            id: pair_3a2ede7ad92a455499ccf2270c7e02a9
          - id: pair_fac23ad957314d34b727f1009a143773
            name: X-Api-Key
            value: "{{ _.rlApiKey }}"
            description: ""
            disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.rlApiBaseUrl }}/cashback"
        name: cashback
        meta:
          id: req_2c40bd69d3cc48299178daf8a5a1649d
          created: 1747722574644
          modified: 1747722653562
          isPrivate: false
          description: ""
          sortKey: -1746694320807
        method: GET
        parameters:
          - id: pair_8fb5182690e14eeb8637f0b8f4e8cdb4
            name: iso2
            value: en
            description: ""
            disabled: false
          - id: pair_d42b1c3fa7f5426ebbb691d9bb86bbcc
            name: market
            value: ROW
            description: ""
            disabled: false
          - id: pair_b77d578921124563ba4ba5c39356a002
            name: tags
            value: display:web-only,display:both
            description: ""
            disabled: false
          - id: pair_7f986fa0c1c6425dbe4633fa34be5625
            name: license
            value: curacao
            description: ""
            disabled: false
        headers:
          - name: Accept
            value: application/json;v=1
            id: pair_3a2ede7ad92a455499ccf2270c7e02a9
          - id: pair_fac23ad957314d34b727f1009a143773
            name: X-Api-Key
            value: "{{ _.rlApiKey }}"
            description: ""
            disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.rlApiBaseUrl }}/promotions"
        name: promotions
        meta:
          id: req_f4ec3d57ed584f8395060c48418cdec9
          created: 1748854728164
          modified: 1749542142240
          isPrivate: false
          description: ""
          sortKey: -1746694320819.5
        method: GET
        parameters:
          - id: pair_8fb5182690e14eeb8637f0b8f4e8cdb4
            name: iso2
            value: en
            description: ""
            disabled: false
          - id: pair_d42b1c3fa7f5426ebbb691d9bb86bbcc
            name: market
            value: ROW
            description: ""
            disabled: false
          - id: pair_b77d578921124563ba4ba5c39356a002
            name: tags
            value: authentication:all,authentication:logged-out
            description: ""
            disabled: false
          - id: pair_7f986fa0c1c6425dbe4633fa34be5625
            name: license
            value: curacao
            description: ""
            disabled: false
          - id: pair_981e400bfa6b46d2bc4c448c3f6d94ad
            name: type
            value: "0"
            description: ""
            disabled: false
          - id: pair_c622d34a59844b8e8e22ae34dbd45d96
            name: page
            value: "1"
            description: ""
            disabled: false
          - id: pair_d7152cb61f5e4f88a7ceb2ea7a5cbdad
            name: limit
            value: "2"
            description: ""
            disabled: false
        headers:
          - name: Accept
            value: application/json;v=3
            id: pair_3a2ede7ad92a455499ccf2270c7e02a9
          - id: pair_fac23ad957314d34b727f1009a143773
            name: X-Api-Key
            value: "{{ _.rlApiKey }}"
            description: ""
            disabled: false
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
  - name: S3 api
    meta:
      id: fld_56e3b6ec48ef4608b87da63b56acf374
      created: 1746694333361
      modified: 1746694350592
      sortKey: -1746694333361
      description: ""
    children:
      - url: "{{ _.s3ApiBaseUrl }}/configs/app-config-global.json"
        name: app-config-global.json
        meta:
          id: req_4a6c8c22db7f4209a9b377c3e7c362ca
          created: 1746694363342
          modified: 1746695004174
          isPrivate: false
          description: ""
          sortKey: -1746694363342
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.1.0
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.s3ApiBaseUrl }}/configs/mobile-app-global-config.json"
        name: mobile-app-config-global.json
        meta:
          id: req_f81263f2fa46470a8af27dc9d8f69e3c
          created: 1746778205792
          modified: 1746778391241
          isPrivate: false
          description: ""
          sortKey: -1746694342112
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.1.0
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.s3ApiBaseUrl }}/configs/app-config_en_ROW.json"
        name: app-config_en_ROW.json
        meta:
          id: req_ad5d86dee5364fa8a940b7c0bb7be147
          created: 1746778554637
          modified: 1746778562738
          isPrivate: false
          description: ""
          sortKey: -1746694352727
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.1.0
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.s3ApiBaseUrl }}/configs/welcome-page_en_ROW.json"
        name: welcome-page_en_ROW.json
        meta:
          id: req_ef333ebfbad4465ea5624f2b95878956
          created: 1747053570045
          modified: 1747053583714
          isPrivate: false
          description: ""
          sortKey: -1746694347419.5
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.1.0
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.s3ApiBaseUrl }}/configs/welcome-page-v2_fi_FI.json"
        name: welcome-page-v2_fi_FI.json
        meta:
          id: req_5b3debdd765b4938a11ad491a78a3644
          created: 1748870020394
          modified: 1749801944983
          isPrivate: false
          description: ""
          sortKey: -1746694344765.75
        method: GET
        headers:
          - name: User-Agent
            value: insomnia/11.1.0
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
cookieJar:
  name: Default Jar
  meta:
    id: jar_476b2fabbc181ea252b75df8cbae5116538cff66
    created: 1744025108235
    modified: 1747662821116
  cookies:
    - key: refreshToken
      value: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      domain: localhost
      path: /auth
      secure: true
      httpOnly: true
      hostOnly: false
      pathIsDefault: true
      creation: 2025-04-10T08:37:46.435Z
      lastAccessed: 2025-05-19T13:53:41.114Z
      id: cbb9aeaf-eb18-4739-8900-f9af1cc4318a
environments:
  name: sweeprush
  meta:
    id: env_476b2fabbc181ea252b75df8cbae5116538cff66
    created: 1744025108233
    modified: 1746694945871
    isPrivate: false
  subEnvironments:
    - name: lh
      meta:
        id: env_5897afd8935f48fcb5180b6fa5dcecb7
        created: 1746694848817
        modified: 1747723254959
        isPrivate: false
        sortKey: 1746694848817
      data:
        rlApiBaseUrl: http://localhost:8880
        s3ApiBaseUrl: http://localhost:8890
        rlApiKey: 8f031c91-0fd6-4cb0-b479-127db06d065b
      color: "#ff3d9b"
    - name: casinodays-staging
      meta:
        id: env_9390e24d820441a4859f82ae5611db98
        created: 1746777883952
        modified: 1747722615980
        isPrivate: false
        sortKey: 1746736357035
      data:
        rlApiBaseUrl: https://api-middleware.dev.rhinoent.net
        s3ApiBaseUrl: https://s3.eu-central-1.amazonaws.com/middleware.assets/staging/casinodays
        rlApiKey: 8f031c91-0fd6-4cb0-b479-127db06d065b
      color: "#ff3d9b"
