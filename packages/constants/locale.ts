export enum RhinoMarkets {
  RestOfWorld = 'en',
  RestOfCanada = 'ca',
  FrenchCanada = 'fr-ca',
  India = 'in',
  NewZealand = 'nz',
  Norway = 'no',
  SouthAfrica = 'za',
  Finland = 'fi',
  Japan = 'jp',
  Thailand = 'th',
  OntarioCanada = 'on',
  Estonia = 'ee',
}

// ISO 639-1 Language Codes https://www.w3schools.com/tags/ref_language_codes.asp
/** Language code values, to do not be confused with ISO_COUNTRY_CODES */
export enum ISO_LANGUAGE_CODES {
  English = 'en',
  Estonian = 'et',
  Finnish = 'fi',
  French = 'fr',
  Norwegian = 'no',
  Japanese = 'ja',
  Thai = 'th',
}

// https://www.w3schools.com/tags/ref_country_codes.asp
export enum ISO_COUNTRY_CODES {
  Canada = 'CA',
  Estonia = 'EE',
  Finland = 'FI',
  India = 'IN',
  Japan = 'JP',
  NewZealand = 'NZ',
  Norway = 'NO',
  SouthAfrica = 'ZA',
  Thailand = 'TH',
  UnitedKingdom = 'GB',
}
export interface IsoCodes {
  language: ISO_LANGUAGE_CODES
  country?: ISO_COUNTRY_CODES
}

export const DEVICES = {
  DESKTOP: 'desktop',
  MOBILE: 'mobile',
  TABLET: 'tablet',
  APP: 'app',
} as const

export const LAYOUT = {
  DESKTOP: 'desktop',
  MOBILE: 'mobile',
  TABLET: 'tablet',
} as const

export const CURRENCY_POSITION = {
  PREFIX: 'prefix',
  SUFFIX: 'suffix',
} as const
