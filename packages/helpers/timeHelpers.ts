import { TimeUnitLabels } from '@repo/constants/sidebar'

/**
 * Formats a number to always display with at least 2 digits by adding leading zeros
 * @param num - The number to format
 * @returns A string with at least 2 digits (e.g., 5 -> "05", 12 -> "12")
 */
export const formatTimeValueWithLeadingZero = (num: number): string => {
  return num.toString().padStart(2, '0')
}

/**
 * Gets the display label for a time unit
 * @param unit - The time unit string (days, hours, minutes, seconds)
 * @returns The corresponding display label from TimeUnitLabels enum
 */
export const getTimeUnitLabel = (unit: string): string => {
  switch (unit) {
    case 'days':
      return TimeUnitLabels.DAYS
    case 'hours':
      return TimeUnitLabels.HOURS
    case 'minutes':
      return TimeUnitLabels.MINUTES
    case 'seconds':
      return TimeUnitLabels.SECONDS
    default:
      return ''
  }
}
