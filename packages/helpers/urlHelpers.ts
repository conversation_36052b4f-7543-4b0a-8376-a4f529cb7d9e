import { Keyable } from '@repo/types/common'

/**
 * @description Checks whether the URL starts with a protocol
 */
export const isUrlAbsolute = (url: string) => {
  const absoluteUrlRegex = /^[a-z][a-z0-9+.-]*:/
  return absoluteUrlRegex.test(url)
}

/**
 *  @description Get the value of a header
 * @param headers
 * @param headerName
 * @returns string | undefined
 */

export const getHeaderValue = (headers: Record<string, string>, headerName: string) => {
  const normalizedHeaderName = headerName.toLowerCase()
  for (const key in headers) {
    if (headers.hasOwnProperty(key) && key.toLowerCase() === normalizedHeaderName) {
      return headers[key]
    }
  }
  return undefined
}

export const createSseUrl = (baseUrl: string, path: string, params: Keyable = {}) => {
  const queryString = Object.keys(params)
    .map(key => `${key}=${encodeURIComponent(params[key])}`)
    .join('&')

  return `${baseUrl}${path}${queryString ? '?' + queryString : ''}`
}

/**
 * @description Removes the trailing slash from a URL if it exists
 * @param url - The URL to process
 * @returns The URL without a trailing slash
 */
export const removeTrailSlash = (url: string): string => {
  return url.replace(/\/$/, '')
}
