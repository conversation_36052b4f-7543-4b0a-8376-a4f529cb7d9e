export const extractLocale = (market: string): [string, string] => {
  if (market === 'fi') {
    return ['fi', 'FI']
  } else if (market === 'in') {
    return ['en', 'IN']
  }

  return ['en', 'ROW'] // TODO - implement locale extraction logic
}

export const getLicense = (market: string) => {
  if (market === 'fi') {
    return 'finland'
  }

  return 'curacao' // TODO - implement license detection logic
}
