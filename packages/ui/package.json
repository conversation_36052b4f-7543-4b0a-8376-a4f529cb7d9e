{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./utils/*": "./utils/*.ts", "./shadcn/*": "./shadcn/*.tsx", "./*": "./src/*.tsx"}, "scripts": {"lint": "eslint .", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@turbo/gen": "^2.4.0", "@types/node": "^22.13.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "eslint": "^9.20.0", "typescript": "^5.7.3"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}