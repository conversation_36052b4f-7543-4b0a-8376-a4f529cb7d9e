import { RHIN<PERSON>AYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS } from '@repo/constants/rhinoLayer'

export interface Image {
  alt: string
  src: string
  width: string
  device: string | 'all' | 'desktop' | 'mobile'
  height: string
  target: string
}

export interface Background {
  src: Image | string
  color: string
}

export interface BorderRadius {
  top?: boolean
  bottom?: boolean
}

export type SectionBase = {
  type: (typeof RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS)[keyof typeof RHINOLAYER_WELCOME_PAGE_V2_COMPONENT_BLOCKS]
  is_hidden: boolean
  background_img?: Image
  preview_config: Record<string, any>
  border_radius_top?: boolean
  border_radius_bottom?: boolean
}

export interface TickerItem {
  icon: string
  title: string
}

export interface BannerTicker {
  items: TickerItem[]
  position: string
  movementSpeed: number
  isTickerEnabled: boolean
}

export interface VideoConfig {
  isvideoenabled: boolean
  videofallbackimgapp: string
}

export interface BannerSection extends SectionBase {
  type: 'banner_section'
  preview_config: {
    background: Background
    borderRadius: BorderRadius
    banner_ticker: BannerTicker
    regular_banner: boolean
    promotion_banner_id: string
    welcome_banner_slug: string
    welcome_offer_video: VideoConfig
    brite_banner_button_type: string
    middleware_promotion_banner_id: string
    backgroundMobile?: Background
  }
}

export interface IconItem {
  icon: Image
  title: string
  content: string
  position: string
}

export interface InfoSection extends SectionBase {
  type: 'info_section'
  preview_config: {
    cta: string
    url: string
    type: string
    items: {
      first: IconItem
      second: IconItem
      third: IconItem
      fourth?: IconItem
    }
    title: string
    is_hidden: boolean
    background: Background
    borderRadius: BorderRadius
    cta_description: string
    backgroundMobile?: Background
  }
}

export interface GamePreview extends SectionBase {
  type: 'game_preview'
  preview_config: {
    games: string[]
    tags?: string[]
    title: string
    is_hidden: boolean
    is_jackpot: boolean
    rows_count: {
      rows_count_mobile: number
      rows_count_desktop: number
    }
    thumbs_type: string
    category_tag?: string
    showGameNumber: boolean
    items_per_row_mobile: number
  }
}

export interface GamesSection extends SectionBase {
  type: 'games_section'
  preview_config: {
    link: string
    tags: Record<string, any>
    games: Record<string, any>
    title: string
    is_hidden: boolean
    is_jackpot: boolean
    description: string
    thumbs_type: string
  }
}

export interface AppSection extends SectionBase {
  type: 'app_section'
  preview_config: {
    items: Array<{
      icon: string
      text: string
    }>
    title: string
    app_cta: {
      url: string
      icon: string
      text: string
    }
    subtitle: string
    background: Background
    borderRadius: BorderRadius
    image_mobile: Image
    'dynamic modal': string
    image_desktop: Image
    backgroundMobile: Background
  }
}

export interface VipSection extends SectionBase {
  type: 'vip_section'
  preview_config: {
    items: {
      first: IconItem
      second: IconItem
      third: IconItem
    }
    title: string
    sub_title: string
    background: Background
    borderRadius: BorderRadius
    backgroundMobile: Background
  }
}

export interface TestimonialItem {
  id: number
  link: string
  title: string
  author: string
  caption: string
  background: string
  description: string
}

export interface TestimonialLander extends SectionBase {
  type: 'testimonial_lander'
  preview_config: {
    items: TestimonialItem[]
    swiper: {
      type: string
    }
    heading: string
    is_hidden: boolean
    background: Background
    backgroundMobile: Background
  }
}

export interface PaymentSection extends SectionBase {
  type: 'payment_section'
  preview_config: {
    title: string
    is_hidden: boolean
    background: Background
    borderRadius: BorderRadius
    payment_icons: string[][]
    backgroundMobile: Background
    isBottomPaymentIconsHidden: boolean
  }
}

export interface FaqSection extends SectionBase {
  type: 'faq_section'
  background_img: Image
  background_img_lg: Image
  preview_config: {
    title: string
    is_hidden: boolean
    category_id: string
    borderRadius: BorderRadius
    show_contact_us_section: boolean
  }
}

export interface SeoSection extends SectionBase {
  type: 'seo_section'
  preview_config: {
    block: Array<{
      content: string
    }>
    is_hidden: boolean
  }
}

export type WelcomePageSection =
  | BannerSection
  | InfoSection
  | GamePreview
  | GamesSection
  | AppSection
  | VipSection
  | TestimonialLander
  | PaymentSection
  | FaqSection
  | SeoSection

export interface WelcomePageV2Response {
  seo_title: string
  seo_description: string
  welcome_page_v2: WelcomePageSection[]
}
