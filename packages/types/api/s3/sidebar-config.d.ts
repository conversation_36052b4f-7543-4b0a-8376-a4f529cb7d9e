type SidebarSectionType = 'promotional-cards' | 'navigation-links' | 'divider' | 'featured-offer' | 'settings' | 'social-media'

export type SidebarContentType = 'main-navigation' | 'chat'

export interface IModularSidebarConfig {
  type: SidebarContentType
  [key: string]: any
}

export interface IMainNavigationData {
  sections?: SidebarSection[]
  error?: string
  isLoading?: boolean
}

interface IBaseSidebarSection<T extends SidebarSectionType> {
  type: T
  hide_for?: {
    auth?: 'logged-in' | 'logged-out'
  }
}

interface IPromotionalCardItem {
  title: string
  thumbnail: string
  url: string
}

export interface IPromotionalCardsSidebarSection extends IBaseSidebarSection<'promotional-cards'> {
  items: IPromotionalCardItem[]
}

interface INavigationLinkItem {
  title: string
  icon_dark: string
  icon: string
  url: string
}

export interface INavigationLinksSidebarSection extends IBaseSidebarSection<'navigation-links'> {
  title: string
  items: INavigationLinkItem[]
}

interface ISidebarDivider extends IBaseSidebarSection<'divider'> { }

export interface IFeaturedOfferSidebarSection extends IBaseSidebarSection<'featured-offer'> {
  id: number
  headline: string
  subline: string
  ctaTitle: string
  backgroundImage: string
  hasIllustration: boolean
}

interface ISocialMediaItem {
  name: string;
  icon: string;
  icon_dark: string;
  url: string
}
export interface ISocialMediaCardsSidebarSection extends IBaseSidebarSection<'social-media'> {
  id: string;
  title: string;
  backgroundPatternUrl?: string;
  items: ISocialMediaItem[]
}

type SettingsItem =
  | { type: 'language-selector' }
  | { type: 'support'; url: string }
  | { type: 'theme-toggle' }

interface ISettingsSidebarSection extends IBaseSidebarSection<'settings'> {
  items: SettingsItem[]
}

type SidebarSection =
  | IPromotionalCardsSidebarSection
  | INavigationLinksSidebarSection
  | ISidebarDivider
  | IFeaturedOfferSidebarSection
  | ISettingsSidebarSection
  | ISocialMediaCardsSidebarSection

export interface ISidebarConfig {
  sections: SidebarSection[]
  type?: string
}

export interface ISidebarConfigGlobal {
  left: ISidebarConfig
  right: ISidebarConfig
}

export type GetSidebarConfigGlobalResponse = ISidebarConfigGlobal