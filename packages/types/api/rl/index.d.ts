import { Keyable } from '../../common'

type RlResponseError = {
  status: 'Error'
  timestamp: string
  data: { message: string; error: string }
}

type RlResponseSuccess<T = Keyable> = {
  status: 'Success'
  timestamp: string
  data: T
}

export type IRlResponse<T = Keyable> = {
  status: 'Success' | 'Error'
  timestamp: string
  data: T
}
export interface IRefreshTokenResponse {
  accessToken: string
  refreshToken: string
}

/**
 * @example
 * {"statusCode":400,"message":["email must be an email"],"error":"Bad Request"}
 */
export interface IValidationError {
  statusCode: number
  message: string[] | string
  error: string // Bad request
  meta?: { key: string; message: string }[] | { [key: string]: { key: string; message: string }[] }
}

export interface IRlPaginationResponse<T> {
  currentPage: number
  totalItems: number
  totalPages: number
  currentItemsCount?: number
  payload: T[]
}
