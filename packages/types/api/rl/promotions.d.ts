import { IRlPaginationResponse } from '.'

export interface IGetPromotionsProps {
  market: string
  license: string
  tags: string[]
  type?: number
  page?: number
  limit?: number
  ids?: number[] | string[]
  isLoggedIn: boolean
  affiliateId?: number | string | null
}

export interface IRlPromotionsResponse extends IRlPaginationResponse<IRlPromotion> {}

export interface IPromotion {
  affiliateIds: string[]
  id: number
  slug: string
  name: string
  caption: string
  description: string
  isWelcomeOffer: boolean
  publishPath: string
  bonusId: string
  expiresAt: string
  ctas: {
    [k: string]: PromotionCta
  }
  title: string
  image: {
    mobile?: string
    desktop?: string
  }
  seo: {
    title: string
    description: string
  }
  terms: PromotionTerms
  tags: { id: number; name: string }[]
  isOngoing: boolean
  ongoingStatus?: string
}

export interface IRlPromotion extends IPromotion {
  startAt: string
  publishPath?: string
  bonusId?: string
  affiliateIds?: string[]
}
