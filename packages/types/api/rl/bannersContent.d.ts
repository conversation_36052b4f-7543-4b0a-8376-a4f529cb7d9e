import { BUTTON_MODES, BUTTON_TYPES } from '@repo/constants/rhinoLayer'
import { ReverseMap } from '../../common'
import { DEVICES, LAYOUT } from '@repo/constants/locale'
import { BannerComponentsEnum } from '@repo/constants/banners'

export interface IBannerContentButtonProps {
  link: string
  mode: ReverseMap<typeof BUTTON_MODES>
  name: string
  position: string
  type: ReverseMap<typeof BUTTON_TYPES>
}

export interface IBannerContentTickerProps {
  items: Array<{
    icon: {
      src: string
    }
    name: string
    position: number
  }>
  movementSpeed: number
}

export interface IBannerContentImageListProps {
  images: Array<{
    alt: string
    src: string
    device: ReverseMap<typeof DEVICES> | 'all'
    target: ReverseMap<typeof LAYOUT> | 'all'
  }>
}

export interface IBannerContentVideoProps {
  backgroundImageMobile: string
  backgroundImageMobileApp: string
  videoLinkMobile: string
  videoLinkMobileApp: string
}

export interface IBannerContentTermsProps {
  content: string
  slug: string
}

export interface IBannerItem<T = BannerComponentsEnum> {
  id: number
  position: number
  type: T
  content: T extends BannerComponentsEnum.ButtonComponent
    ? IBannerContentButtonProps
    : T extends BannerComponentsEnum.TickerComponent
      ? IBannerContentTickerProps
      : T extends BannerComponentsEnum.ImageListComponent
        ? IBannerContentImageListProps
        : T extends BannerComponentsEnum.VideoComponent
          ? IBannerContentVideoProps
          : T extends BannerComponentsEnum.TermsComponent
            ? IBannerContentTermsProps
            : T extends BannerComponentsEnum.TextComponent
              ? string
              : never
}

export interface IRlBannerItem {
  startsAt: string
  expiresAt: string
  id: number
  name: string
  order: number
  segments: string[]
  items: Array<IBannerItem>
  affiliateIds?: string[]
}

export interface IRlBanner {
  id: number
  name: string
  slug: string
  tags: Array<{
    id: number
    name: string
  }>
  bannerItems?: Array<IRlBannerItem>
}
