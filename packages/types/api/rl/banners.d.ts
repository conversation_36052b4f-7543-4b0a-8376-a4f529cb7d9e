import { IRlBannerItem } from './bannersContent'

export interface IGetBannersProps {
  license: string
  market: string
  affiliateId?: string | number
  tags?: string[]
  ids?: number[]
}

export interface IGetBannersResponse {
  status: string
  timestamp: string
  data: {
    payload: Array<{
      id: number
      slug: string
      name: string
      tags: Array<{
        id: number
        name: string
      }>
      bannerItems: IRlBannerItem[]
    }>
    totalItems: number
    totalPages: number
    currentPage: number
  }
}
