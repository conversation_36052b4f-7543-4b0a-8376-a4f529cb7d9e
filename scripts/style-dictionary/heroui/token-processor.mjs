// Token processing utilities for HeroUI config generation

import { TOKEN_MAP } from './tokens-map.mjs'
import { 
  capitalize, 
  getNestedValue, 
} from './helpers.mjs'
import { generateColorShades, isValidColor } from './shade-generator.mjs'

// Function to extract first color from gradient or return the color as-is
function extractColorFromGradient(colorValue) {
  const colorMatches = colorValue.match(/#[0-9A-Fa-f]{3,8}|rgb\([^)]+\)|rgba\([^)]+\)|hsl\([^)]+\)|hsla\([^)]+\)/g)

  if (colorMatches && colorMatches.length > 0) {
    console.log(`   🎨 Extracted '${colorMatches[0]}' from gradient: ${colorValue}`)
    return colorMatches[0]
  }

  // Fallback: try to extract any color-like value after percentage
  const fallbackMatches = colorValue.match(/\d+%[^,)]*([^,)]+)/g)
  if (fallbackMatches && fallbackMatches.length > 0) {
    const extracted = fallbackMatches[0].replace(/\d+%\s*/, '').trim()
    console.log(`   🎨 Extracted '${extracted}' from gradient (fallback): ${colorValue}`)
    return extracted
  }

  console.log(`   ⚠️  Could not extract color from gradient: ${colorValue}`)
  return colorValue // Return original if we can't parse it
}

/**
 * Process colors for a single theme
 * @param {string} themeName - Name of the theme (light/dark)
 * @param {Object} themeData - Theme data from style dictionary
 * @param {Object} themeConfig - Theme configuration object to populate
 */
export function processColors(themeName, themeData, themeConfig) {
  console.log(`   🎨 Processing colors for ${themeName} theme...`)
  Object.entries(TOKEN_MAP.colors).forEach(([colorKey, mapping]) => {
    const tokenPath = mapping.color
    if (!tokenPath) return

    const rawValue = getNestedValue(themeData, tokenPath)
    if (rawValue !== undefined) {
      const processedValue = rawValue?.includes('gradient(') ? extractColorFromGradient(rawValue) : rawValue

      // Check if this should be a singular value
      if (mapping.isSingleValue) {
        // For singular values, just assign the processed value directly
        themeConfig.colors[colorKey] = processedValue
        console.log(`   ✓ ${capitalize(themeName)} ${colorKey}: ${tokenPath} -> ${processedValue} (singular value)`)
        return
      }

      // Get foreground color if available
      let foregroundColor = null
      if (mapping.foreground) {
        const foregroundRawValue = getNestedValue(themeData, mapping.foreground)
        if (foregroundRawValue !== undefined) {
          foregroundColor = foregroundRawValue?.includes('gradient(')
            ? extractColorFromGradient(foregroundRawValue)
            : foregroundRawValue
        }
      }

      // Check if this should only have DEFAULT and foreground (no shades)
      if (mapping.isDefaultOnly) {
        const colorObj = { DEFAULT: processedValue }
        if (foregroundColor && isValidColor(foregroundColor)) {
          colorObj.foreground = foregroundColor
        } else {
          colorObj.foreground = '#000000' // Default to black when no foreground color is provided
        }
        themeConfig.colors[colorKey] = colorObj
        const foregroundMsg = foregroundColor
          ? ` + foreground: ${foregroundColor}`
          : ' + foreground: #000000 (fallback)'
        console.log(
          `   ✓ ${capitalize(themeName)} ${colorKey}: ${tokenPath} -> ${processedValue} (DEFAULT only${foregroundMsg})`,
        )
        return
      }

      // Validate the color before generating shades
      if (isValidColor(processedValue)) {
        themeConfig.colors[colorKey] = generateColorShades(processedValue, foregroundColor)
        const foregroundMsg = foregroundColor
          ? ` + foreground: ${foregroundColor}`
          : ' + foreground: #000000 (fallback)'
        console.log(
          `   ✓ ${capitalize(themeName)} ${colorKey}: ${tokenPath} -> ${processedValue} (with shades${foregroundMsg})`,
        )
      } else {
        // Fallback to just DEFAULT if color is invalid, but still include foreground
        const colorObj = { DEFAULT: processedValue }
        if (foregroundColor && isValidColor(foregroundColor)) {
          colorObj.foreground = foregroundColor.toUpperCase()
        } else {
          colorObj.foreground = '#000000' // Default to black when no foreground color is provided
        }
        themeConfig.colors[colorKey] = colorObj
        const foregroundMsg = foregroundColor
          ? ` + foreground: ${foregroundColor}`
          : ' + foreground: #000000 (fallback)'
        console.log(
          `   ❕  ${capitalize(themeName)} ${colorKey}: ${tokenPath} -> ${processedValue} (invalid color, no shades${foregroundMsg})`,
        )
      }
    } else {
      console.log(`   ⚠️  ${capitalize(themeName)} ${colorKey}: ${tokenPath} not found`)
    }
  })
}

/**
 * Process layout tokens for the config (theme-agnostic)
 * @param {string} themeName - Name of the theme (for logging)
 * @param {Object} themeData - Theme data from style dictionary
 * @param {Object} config - Main configuration object to populate
 */
export function processLayout(themeName, themeData, config) {
  console.log(`   📐 Processing layout tokens (theme-agnostic)...`)
  Object.entries(TOKEN_MAP.layout).forEach(([layoutKey, mapping]) => {
    if (typeof mapping === 'string') {
      // Simple string token path
      const rawValue = getNestedValue(themeData, mapping)
      if (rawValue !== undefined) {
        config.layout[layoutKey] = rawValue
        console.log(`   ✓ ${layoutKey}: ${mapping} -> ${rawValue}`)
      } else {
        console.log(`   ⚠️  ${layoutKey}: ${mapping} not found`)
      }
    } else if (typeof mapping === 'object' && mapping !== null) {
      // Nested object with multiple values
      config.layout[layoutKey] = {}
      Object.entries(mapping).forEach(([subKey, tokenPath]) => {
        const rawValue = getNestedValue(themeData, tokenPath)
        if (rawValue !== undefined) {
          config.layout[layoutKey][subKey] = rawValue
          console.log(`   ✓ ${layoutKey}.${subKey}: ${tokenPath} -> ${rawValue}`)
        } else {
          console.log(`   ⚠️  ${layoutKey}.${subKey}: ${tokenPath} not found`)
        }
      })
    }
  })
}
