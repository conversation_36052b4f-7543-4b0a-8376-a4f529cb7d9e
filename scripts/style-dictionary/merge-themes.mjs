#!/usr/bin/env node

import fs from 'fs/promises'
import path from 'path'
import { getImmediateChildren } from './utils/getImmediateChildren.mjs'

/**
 * Merges dark theme variables into the main style dictionary files for all brands
 * Takes variables from style-dictionary-dark.css :root and adds them under a .dark class
 */
async function mergeThemes() {
  try {
    const brands = getImmediateChildren('apps', { directoriesOnly: true })
    
    let processedCount = 0

    for (const brand of brands) {
      const appPath = `apps/${brand}/src/theme`
      const mainFilePath = path.join(appPath, 'style-dictionary.css')
      const darkFilePath = path.join(appPath, 'style-dictionary-dark.css')

      try {
        await fs.access(mainFilePath)
        await fs.access(darkFilePath)
      } catch (error) {
        continue
      }
      
      await processBrandThemes(brand, mainFilePath, darkFilePath)
      console.log(`Merged dark theme into default theme for ${brand}: ${mainFilePath}`)
      processedCount++
    }

    if (processedCount === 0) {
      console.error('Theme merge failed: No themes processed')
    }

  } catch (error) {
    console.error('Theme merge failed:', error.message)
    process.exit(1)
  }
}

/**
 * Process theme files for a specific brand
 */
async function processBrandThemes(brand, mainFilePath, darkFilePath) {
  try {
    // Read both files
    const [mainContent, darkContent] = await Promise.all([
      fs.readFile(mainFilePath, 'utf8'),
      fs.readFile(darkFilePath, 'utf8')
    ])

    const darkVariables = extractVariablesFromRoot(darkContent)
    
    if (darkVariables.length === 0) {
      return
    }

    const hasDarkClass = mainContent.includes('.dark {')
    
    let mergedContent
    if (hasDarkClass) {
      mergedContent = replaceDarkClass(mainContent, darkVariables)
    } else {
      mergedContent = addDarkClass(mainContent, darkVariables)
    }

    await fs.writeFile(mainFilePath, mergedContent, 'utf8')
    
    await fs.unlink(darkFilePath)
    
    console.log(`Generated: ${mainFilePath}`)

  } catch (error) {
    console.error(`Error processing ${brand} themes:`, error.message)
    throw error
  }
}

/**
 * Extracts CSS variables from :root selector
 */
function extractVariablesFromRoot(cssContent) {
  const variables = []
  
  const rootMatch = cssContent.match(/:root\s*{([^}]*)}/s)
  if (!rootMatch) {
    return variables
  }

  const rootContent = rootMatch[1]
  
  const variableMatches = rootContent.match(/--[^:]+:[^;]+;/g)
  if (variableMatches) {
    variables.push(...variableMatches.map(variable => variable.trim()))
  }

  return variables
}

/**
 * Adds .dark class with variables to the end of the CSS file
 */
function addDarkClass(cssContent, darkVariables) {
  const darkClass = `
.dark {
  ${darkVariables.join('\n  ')}
}`

  return cssContent.trimEnd() + '\n' + darkClass + '\n'
}

/**
 * Replaces existing .dark class with new variables
 */
function replaceDarkClass(cssContent, darkVariables) {
  const darkClass = `.dark {
  ${darkVariables.join('\n  ')}
}`

  return cssContent.replace(
    /\.dark\s*{[^}]*}/s,
    darkClass
  )
}

mergeThemes()
