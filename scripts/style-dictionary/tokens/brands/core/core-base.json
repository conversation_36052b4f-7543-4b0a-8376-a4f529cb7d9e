{"a-very-core-token": {"value": "#a8a8a8", "type": "color"}, "radius": {"none": {"value": "0rem", "type": "dimension"}, "xxs": {"value": "0.25rem", "type": "dimension"}, "xs": {"value": "0.375rem", "type": "dimension"}, "sm": {"value": "0.5rem", "type": "dimension"}, "md": {"value": "0.75rem", "type": "dimension"}, "lg": {"value": "1rem", "type": "dimension"}, "full": {"value": "1000rem", "type": "dimension"}}, "font": {"family": {"primary": {"value": "Roboto", "type": "fontFamilies"}, "secondary": {"value": "Montserrat", "type": "fontFamilies"}}, "weight": {"extrabold": {"value": "900", "type": "fontWeights"}}, "size": {"xs": {"value": "0.75rem", "type": "dimension"}, "sm": {"value": "0.875rem", "type": "dimension"}, "md": {"value": "1rem", "type": "dimension"}, "lg": {"value": "1.125rem", "type": "dimension"}, "xl": {"value": "1.25rem", "type": "dimension"}, "2xl": {"value": "1.5rem", "type": "dimension"}, "3xl": {"value": "2rem", "type": "dimension"}, "4xl": {"value": "3rem", "type": "dimension"}}, "lineheight": {"tight": {"value": "1", "type": "number"}, "normal": {"value": "1.2", "type": "number"}}}, "typography": {"label": {"sm": {"value": {"fontFamily": "{font.family.primary}", "fontSize": "{font.size.sm}", "lineHeight": "{font.lineheight.tight} * 100%", "fontWeight": "{font.weight.extrabold}"}, "type": "typography"}, "md": {"value": {"fontFamily": "{font.family.primary}", "fontSize": "{font.size.md}", "lineHeight": "{font.lineheight.tight} * 100%", "fontWeight": "{font.weight.extrabold}"}, "type": "typography"}}}, "size": {"xxs": {"value": "0.25rem", "type": "dimension"}, "xs": {"value": "0.5rem", "type": "dimension"}, "sm": {"value": "0.75rem", "type": "dimension"}, "md": {"value": "1rem", "type": "dimension"}, "lg": {"value": "1.25rem", "type": "dimension"}, "xl": {"value": "1.5rem", "type": "dimension"}, "2xl": {"value": "2rem", "type": "dimension"}, "2xl-1": {"value": "2.25rem", "type": "dimension"}, "3xl-2": {"value": "2.5rem", "type": "dimension"}, "3xl-3": {"value": "2.75rem", "type": "dimension"}, "3xl": {"value": "3rem", "type": "dimension"}}, "border": {"width": {"none": {"value": "0px", "type": "dimension"}, "default": {"value": "1px", "type": "dimension"}}}, "icon": {"size": {"xs": {"value": "1rem", "type": "dimension"}, "sm": {"value": "1.25rem", "type": "dimension"}, "md": {"value": "1.5rem", "type": "dimension"}}}}