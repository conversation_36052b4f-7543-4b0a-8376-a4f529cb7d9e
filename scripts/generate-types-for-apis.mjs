import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { generateInterface } from './helpers/interfaceGenerator.mjs'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

const S3Api = {
  baseUrl: 'http://localhost:8890',
  endpoints: [
    // { name: 'getAppConfigGlobal', url: '/configs/app-config-global.json' },
    // { name: 'getAppConfig', url: '/configs/app-config_en_ROW.json' },
    { name: 'getCasinoConfig', url: '/configs/casino_en_ROW.json' },
    // { name: 'getSidebarConfigGlobal', url: '/configs/sidebar-config-global.json' },
    // { name: 'getCasinoPageLayoutConfigGlobal', url: '/configs/casino-page-config-global.json' },
  ],
}

const RLApi = {
  baseUrl: 'http://localhost:8880',
  endpoints: [{ name: 'getBanners', url: '/banners' }],
}

const API = S3Api

async function fetchEndpoint(url) {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'application/json;v=1',
      },
    })

    if (!response.ok) {
      throw new Error(`[HTTP error] status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error(`Error fetching ${url}:`, error)
    return null
  }
}

async function main() {
  const interfaces = []

  for (const endpoint of API.endpoints) {
    const url = `${API.baseUrl}${endpoint.url}`
    const json = await fetchEndpoint(url)
    if (json) {
      const interfaceName = `${endpoint.name.charAt(0).toUpperCase() + endpoint.name.slice(1)}Response`
      const tsInterface = generateInterface(interfaceName, json)
      interfaces.push(tsInterface)
    }
  }
  const allInterfaces = [...interfaces]

  const outputPath = path.join(__dirname, 'output/generated-interfaces.d.ts')
  fs.writeFileSync(outputPath, allInterfaces.join('\n\n'))
  console.log(`Interfaces generated and saved to ${outputPath}`)
}

main().catch(error => console.error('Error in script execution:', error))
