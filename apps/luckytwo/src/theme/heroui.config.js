// This file was auto-generated by generate-heroui-config.mjs
// Do not edit directly - run "yarn generate-heroui-config" to regenerate

export default {
  themes: {
    light: {
      colors: {
        primary: {
          50: '#e5f7ff',
          100: '#b3e8ff',
          200: '#80d9ff',
          300: '#4dc9ff',
          400: '#1abaff',
          500: '#00b2ff',
          600: '#00a0e6',
          700: '#007db3',
          800: '#005980',
          900: '#00354d',
          DEFAULT: '#00b2ff',
          foreground: '#0a1a2a',
        },
        secondary: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#ffffff',
          300: '#ffffff',
          400: '#ffffff',
          500: '#f6f9ff',
          600: '#dce8ff',
          700: '#a9c6ff',
          800: '#76a4ff',
          900: '#4382ff',
          DEFAULT: '#f6f9ff',
          foreground: '#1e2a38',
        },
        success: {
          50: '#eafaf2',
          100: '#c1f1d9',
          200: '#97e7c0',
          300: '#6ddea7',
          400: '#44d58e',
          500: '#2fd082',
          600: '#2abb75',
          700: '#21925b',
          800: '#186841',
          900: '#0e3e27',
          DEFAULT: '#2fd082',
          foreground: '#ffffff',
        },
        danger: {
          50: '#fcf4f4',
          100: '#f3cbcb',
          200: '#e9a1a1',
          300: '#df7878',
          400: '#d64f4f',
          500: '#d13a3a',
          600: '#c42e2e',
          700: '#9a2424',
          800: '#711a1a',
          900: '#481111',
          DEFAULT: '#d13a3a',
          foreground: '#000000',
        },
        warning: {
          50: '#fcf4f4',
          100: '#f3cbcb',
          200: '#e9a1a1',
          300: '#df7878',
          400: '#d64f4f',
          500: '#d13a3a',
          600: '#c42e2e',
          700: '#9a2424',
          800: '#711a1a',
          900: '#481111',
          DEFAULT: '#d13a3a',
          foreground: '#000000',
        },
        background: '#e7f3f8',
        foreground: '#97b1c9',
        focus: '#0099e1',
        overlay: '#97b1c9',
        default: {
          50: '#7ca6bd',
          100: '#5a8fac',
          200: '#47748d',
          300: '#36586b',
          400: '#253c49',
          500: '#1c2e38',
          600: '#132027',
          700: '#020405',
          800: '#000000',
          900: '#000000',
          DEFAULT: '#1c2e38',
          foreground: '#f7faff',
        },
        content1: {
          DEFAULT: '#00b2ff',
          foreground: '#0a1a2a',
        },
        content2: {
          DEFAULT: '#f6f9ff',
          foreground: '#1e2a38',
        },
        content3: {
          DEFAULT: '#1CB2C2',
          foreground: '#ffffff',
        },
        content4: {
          DEFAULT: '#1CB2C2',
          foreground: '#ffffff',
        },
      },
    },
    dark: {
      colors: {
        primary: {
          50: '#ffffff',
          100: '#d6f6ff',
          200: '#a3eaff',
          300: '#70deff',
          400: '#3dd2ff',
          500: '#24ccff',
          600: '#0ac6ff',
          700: '#00a5d6',
          800: '#007da3',
          900: '#005670',
          DEFAULT: '#24ccff',
          foreground: '#0a1a2a',
        },
        secondary: {
          50: '#77a2c1',
          100: '#548ab1',
          200: '#427091',
          300: '#32556e',
          400: '#223a4a',
          500: '#1a2c39',
          600: '#121e27',
          700: '#020304',
          800: '#000000',
          900: '#000000',
          DEFAULT: '#1a2c39',
          foreground: '#ffffff',
        },
        success: {
          50: '#eafaf2',
          100: '#c1f1d9',
          200: '#97e7c0',
          300: '#6ddea7',
          400: '#44d58e',
          500: '#2fd082',
          600: '#2abb75',
          700: '#21925b',
          800: '#186841',
          900: '#0e3e27',
          DEFAULT: '#2fd082',
          foreground: '#ffffff',
        },
        danger: {
          50: '#fcf4f4',
          100: '#f3cbcb',
          200: '#e9a1a1',
          300: '#df7878',
          400: '#d64f4f',
          500: '#d13a3a',
          600: '#c42e2e',
          700: '#9a2424',
          800: '#711a1a',
          900: '#481111',
          DEFAULT: '#d13a3a',
          foreground: '#000000',
        },
        warning: {
          50: '#fcf4f4',
          100: '#f3cbcb',
          200: '#e9a1a1',
          300: '#df7878',
          400: '#d64f4f',
          500: '#d13a3a',
          600: '#c42e2e',
          700: '#9a2424',
          800: '#711a1a',
          900: '#481111',
          DEFAULT: '#d13a3a',
          foreground: '#000000',
        },
        background: '#13232b',
        foreground: '#3b4e5e',
        focus: '#00b2e1',
        overlay: '#3b4e5e',
        default: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#ffffff',
          300: '#ffffff',
          400: '#ffffff',
          500: '#f0f8ff',
          600: '#d6ecff',
          700: '#a3d4ff',
          800: '#70bcff',
          900: '#3da5ff',
          DEFAULT: '#f0f8ff',
          foreground: '#181f23',
        },
        content1: {
          DEFAULT: '#24ccff',
          foreground: '#0a1a2a',
        },
        content2: {
          DEFAULT: '#1A2C39',
          foreground: '#ffffff',
        },
        content3: {
          DEFAULT: '#1CB2C2',
          foreground: '#ffffff',
        },
        content4: {
          DEFAULT: '#1CB2C2',
          foreground: '#ffffff',
        },
      },
    },
  },
  layout: {
    radius: {
      small: '0.5rem',
      medium: '0.75rem',
      large: '1rem',
    },
    borderWidth: {
      small: '1px',
      medium: '1px',
      large: '1px',
    },
  },
}
