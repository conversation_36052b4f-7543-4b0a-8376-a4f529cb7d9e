import { NextResponse, type NextRequest } from 'next/server'
import { serverConfig } from '@/config/serverConfig'
import { authService } from '@/services/auth.service'
import { getAppRouter } from '@/services/router.service'
import { detectMarketByRequest } from '@/utils/server/network'

export async function authMiddleware(request: NextRequest) {
  const publicRoutes = ['', ...serverConfig.publicRoutes]
  const supportedMarkets = serverConfig.supportedMarkets
  const { pathname } = request.nextUrl

  const isPublicRoute = publicRoutes.some(
    route => pathname === route || supportedMarkets.some(market => pathname === `/${market}${route}`),
  )

  if (pathname.endsWith('/login')) {
    const isAuthenticated = await authService.isAuthenticated()
    if (isAuthenticated) {
      const currentMarket = (await detectMarketByRequest(request)) || serverConfig.defaultMarket
      const appRouter = getAppRouter(currentMarket)
      return NextResponse.redirect(new URL(appRouter.home, request.url))
    }
  }

  if (!isPublicRoute) {
    const isAuthenticated = await authService.isAuthenticated()
    if (!isAuthenticated) {
      const currentMarket = (await detectMarketByRequest(request)) || serverConfig.defaultMarket
      console.info('[Auth Middleware] Redirect to login', { currentMarket }, await detectMarketByRequest(request))
      const appRouter = getAppRouter(currentMarket)
      return NextResponse.redirect(new URL(appRouter.login, request.url))
    }
  }
}
