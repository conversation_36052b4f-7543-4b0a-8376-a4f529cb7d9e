import { NextRequest, NextResponse } from 'next/server'
import { serverConfig } from '@/config/serverConfig'
import { marketMiddleware } from '@/middlewares/marketMiddleware'
import { authService } from '@/services/auth.service'
import { detectMarketByRequest } from '@/utils/server/network'

jest.mock('@/utils/server/network', () => ({
  detectMarketByRequest: jest.fn(),
}))

jest.mock('@/services/auth.service', () => ({
  authService: {
    isAuthenticated: jest.fn(),
  },
}))

// Mock the console.warn to avoid test output noise
const originalConsoleWarn = console.warn
beforeAll(() => {
  console.warn = jest.fn()
})

afterAll(() => {
  console.warn = originalConsoleWarn
})

describe('marketMiddleware', () => {
  const mockRequest = (url: string) => new NextRequest(new URL(url))

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('redirects to default market if no market is detected', async () => {
    ;(detectMarketByRequest as jest.Mock).mockResolvedValue(null)
    serverConfig.defaultMarket = 'en'
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/some-path')
    const response = await marketMiddleware(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response?.headers.get('location')).toBe('http://example.com/en/some-path')
  })

  it('redirects to detected market if supported', async () => {
    ;(detectMarketByRequest as jest.Mock).mockResolvedValue('fr')
    serverConfig.defaultMarket = 'en'
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/some-path')
    const response = await marketMiddleware(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response?.headers.get('location')).toBe('http://example.com/fr/some-path')
  })

  it('redirects to default market if detected market is unsupported', async () => {
    ;(detectMarketByRequest as jest.Mock).mockResolvedValue('es')
    serverConfig.defaultMarket = 'en'
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/some-path')
    const response = await marketMiddleware(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response?.headers.get('location')).toBe('http://example.com/en/some-path')
    expect(console.warn).toHaveBeenCalledWith('Detected market es is not supported. Defaulting to en.')
  })

  it('redirects authenticated users to their market', async () => {
    ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
    ;(detectMarketByRequest as jest.Mock).mockResolvedValue('fr')
    serverConfig.defaultMarket = 'en'
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/some-path')
    const response = await marketMiddleware(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response?.headers.get('location')).toBe('http://example.com/fr/some-path')
  })

  it('redirects unauthenticated users with unsupported market prefix to detected market', async () => {
    ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
    ;(detectMarketByRequest as jest.Mock).mockResolvedValue('fr')
    serverConfig.defaultMarket = 'en'
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/es/some-path')
    const response = await marketMiddleware(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response?.headers.get('location')).toBe('http://example.com/fr/some-path')
  })

  // Additional test cases for better coverage
  it('does not redirect for paths containing file extensions', async () => {
    ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/assets/styles.css')
    const response = await marketMiddleware(request)

    expect(response).toBeUndefined()
  })

  it('does not redirect for paths already containing supported market', async () => {
    ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(false)
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/en/some-path')
    const response = await marketMiddleware(request)

    expect(response).toBeUndefined()
  })

  it('does not redirect authenticated users if path already contains their market', async () => {
    ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
    ;(detectMarketByRequest as jest.Mock).mockResolvedValue('fr')
    serverConfig.defaultMarket = 'en'
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/fr/dashboard')
    const response = await marketMiddleware(request)

    expect(response).toBeUndefined()
  })

  it('redirects authenticated users with incorrect market prefix to their market', async () => {
    ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
    ;(detectMarketByRequest as jest.Mock).mockResolvedValue('fr')
    serverConfig.defaultMarket = 'en'
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/en/dashboard')
    const response = await marketMiddleware(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response?.headers.get('location')).toBe('http://example.com/fr/dashboard')
  })

  it('redirects authenticated users to default market when their detected market is unsupported', async () => {
    ;(authService.isAuthenticated as jest.Mock).mockResolvedValue(true)
    ;(detectMarketByRequest as jest.Mock).mockResolvedValue('es')
    serverConfig.defaultMarket = 'en'
    serverConfig.supportedMarkets = ['en', 'fr']

    const request = mockRequest('http://example.com/fr/dashboard')
    const response = await marketMiddleware(request)

    expect(response).toBeInstanceOf(NextResponse)
    expect(response?.headers.get('location')).toBe('http://example.com/en/dashboard')
    expect(console.warn).toHaveBeenCalledWith('Detected market es is not supported. Defaulting to en.')
  })
})
