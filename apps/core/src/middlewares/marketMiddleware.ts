import { NextResponse, type NextRequest } from 'next/server'
import { serverConfig } from '@/config/serverConfig'
import { marketUrlSegmentRegex, supportedMarketRegex } from '@/middlewares/utils'
import { authService } from '@/services/auth.service'
import { detectMarketByRequest } from '@/utils/server/network'

function replaceMarketInPath(pathname: string, currentMarket: string) {
  if (marketUrlSegmentRegex.test(pathname)) {
    return pathname.replace(marketUrlSegmentRegex, `/${currentMarket}`)
  }
  return `/${currentMarket}${pathname}`
}

export async function marketMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  const isNormalizedRoute = pathname.includes('.') || supportedMarketRegex.test(pathname)

  if (!isNormalizedRoute) {
    let currentMarket = (await detectMarketByRequest(request)) || serverConfig.defaultMarket

    if (!serverConfig.supportedMarkets.includes(currentMarket)) {
      console.warn(`Detected market ${currentMarket} is not supported. Defaulting to ${serverConfig.defaultMarket}.`)
      currentMarket = serverConfig.defaultMarket
    }

    const newPath = replaceMarketInPath(pathname, currentMarket)

    return NextResponse.redirect(new URL(newPath, request.url))
  }

  const isAuthenticated = await authService.isAuthenticated()

  if (isAuthenticated) {
    let userMarket = (await detectMarketByRequest(request)) || serverConfig.defaultMarket
    if (!serverConfig.supportedMarkets.includes(userMarket)) {
      console.warn(`Detected market ${userMarket} is not supported. Defaulting to ${serverConfig.defaultMarket}.`)
      userMarket = serverConfig.defaultMarket
    }

    // Handle cases like '/ee/dashboard' or '/mk/ee/dashboard'
    if (!pathname.startsWith(`/${userMarket}`)) {
      const newPath = replaceMarketInPath(pathname, userMarket)
      return NextResponse.redirect(new URL(newPath, request.url))
    }
  }
}
