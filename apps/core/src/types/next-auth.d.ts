/* eslint-disable @typescript-eslint/naming-convention */
import type { IRLUserSession } from '@/types/auth'
import 'next-auth'

declare module 'next-auth' {
  interface JWT {
    accessToken: string
    refreshToken: string
    userInfo: IRLUserSession
  }

  interface Session {
    accessToken: string
    refreshToken: string
    user: IRLUserSession
  }

  export interface User {
    accessToken: string
    refreshToken: string
    userInfo: IRLUserSession
  }
}
