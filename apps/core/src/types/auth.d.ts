interface IRlUser {
  id: string
  rvpSessionId: string
  riverplayUrl: string
  pragmaticSessionId: string
  pragmaticUrl: string
  partnerId: string
}

interface IGeo {
  countryCode: string
  state: string
  city: string
}

export interface IRLUserSession {
  success: boolean
  id: string
  sessionLimit: number
  ecrCategory: string
  language: string
  rvpSessionId: string
  riverplayUrl: string
  pragmaticSessionId: string
  pragmaticUrl: string
  mobileVerificationStatus: string
  emailVerificationStatus: string
  twoFactorAuthEnabled: boolean
  user: User
  lastLoginTime: string
  userJurisdiction: string
  jurisdictionResponse: string
  postAuthPopUpDetails: any[]
  policies: any[]
  geo: IGeo
}
