'use client'
import { createContext, useContext } from 'react'

const MarketContext = createContext<string | undefined>(undefined)

export const MarketProvider = ({ market, children }: { market: string; children: React.ReactNode }) => {
  return <MarketContext.Provider value={market}>{children}</MarketContext.Provider>
}

export const useMarket = () => {
  const context = useContext(MarketContext)
  if (context === undefined) {
    throw new Error('useMarket must be used within a MarketProvider')
  }
  return context
}
