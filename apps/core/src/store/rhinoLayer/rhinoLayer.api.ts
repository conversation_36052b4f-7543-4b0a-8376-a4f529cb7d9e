import { envVars } from '@/env'
import promotionsSettings from '@/modules/promotions/promotions.settings'
import { getPromotionsTransformResponse } from '@/modules/promotions/promotions.utils'
import { axiosBaseQuery } from '@/network/baseQuery'
import { createApi } from '@reduxjs/toolkit/query/react'
import { extractLocale } from '@repo/helpers/locale'
import type { IRlResponse } from '@repo/types/api/rl/index'
import type { IGetPromotionsProps, IRlPromotionsResponse } from '@repo/types/api/rl/promotions'

export const rhinolayerApi = createApi({
  reducerPath: 'api.rhinolayer',
  baseQuery: axiosBaseQuery({
    baseUrl: envVars.NEXT_PUBLIC_BACKEND_URL || 'undefined',
    prepareHeaders(headers) {
      headers.set('Accept', 'application/json;v=1')
      return headers
    },
    skipToastForErrorCodes: [400],
  }),
  keepUnusedDataFor: 30,
  tagTypes: ['PROMOTIONS'],
  endpoints: builder => ({
    getBtag: builder.query<any, any>({
      query: ({ market, license = 'unknown' }) => {
        const [iso2, rlMarket] = ['en', 'US']
        return {
          url: '/player/btag',
          params: {
            iso2,
            market: rlMarket,
            license,
          },
        }
      },
      forceRefetch: () => true,
    }),
    getPromotions: builder.query<IRlPromotionsResponse | undefined, IGetPromotionsProps>({
      query: ({ market, license = 'unknown', tags, ids, isLoggedIn, type = 0, page = 1, limit = 20, affiliateId }) => {
        const [iso2, rlMarket] = extractLocale(market)

        return {
          url: '/promotions',
          params: {
            iso2,
            market: rlMarket,
            license,
            tags: (tags || []).join(','),
            ...(ids?.length && { ids: (ids || []).join(',') }),
            page,
            limit,
            type,
            affiliateIds: affiliateId,
          },
          headers: {
            Accept: 'application/json;v=3',
          },
        }
      },
      transformResponse: (response: IRlResponse<IRlPromotionsResponse>, meta, args) => {
        return getPromotionsTransformResponse(response, { isLoggedIn: args.isLoggedIn || false })
      },
      providesTags: ['PROMOTIONS'],
    }),
    getPromotionsPaginated: builder.infiniteQuery<IRlPromotionsResponse, Omit<IGetPromotionsProps, 'page'>, number>({
      query: arg => {
        const { market, license, tags, ids, type, limit, affiliateId, isLoggedIn } = arg.queryArg
        const page = arg.pageParam || 0
        const [iso2, rlMarket] = extractLocale(market)
        console.log(
          `Fetching promotions for market: ${market}, iso2: ${iso2}, rlMarket: ${rlMarket}, 
          license: ${license}, tags: ${tags}, ids: ${ids}, isLoggedIn: ${isLoggedIn}, 
          type: ${type}, page: ${page}, limit: ${limit}, affiliateId: ${affiliateId}`,
        )

        return {
          url: '/promotions',
          params: {
            iso2,
            market: rlMarket,
            license,
            tags: (tags || []).join(','),
            ...(ids?.length && { ids: (ids || []).join(',') }),
            page,
            limit,
            type,
            affiliateIds: affiliateId,
          },
          headers: {
            Accept: 'application/json;v=3',
          },
        }
      },
      transformResponse: (response: IRlResponse<IRlPromotionsResponse>, meta, args) => {
        return getPromotionsTransformResponse(response, { isLoggedIn: args.queryArg.isLoggedIn })
      },
      infiniteQueryOptions: {
        initialPageParam: 1,
        getNextPageParam: (lastPage, allPages, lastPageParam) => {
          const perPage = promotionsSettings.PER_PAGE
          const currentItemsCount = lastPage.currentItemsCount || 0

          if (currentItemsCount < perPage) {
            return undefined
          }
          return lastPageParam + 1
        },
      },
      providesTags: ['PROMOTIONS'],
    }),
  }),
})

export const {
  useGetBtagQuery,
  useGetPromotionsQuery,
  useLazyGetPromotionsQuery,
  useGetPromotionsPaginatedInfiniteQuery,
} = rhinolayerApi
