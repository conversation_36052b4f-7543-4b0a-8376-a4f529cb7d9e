import { z } from 'zod'

const serverEnvSchema = z.object({
  NEXT_PUBLIC_APP_NAME: z.string(),
  SUPPORTED_MARKETS: z.string().transform(val => val.split(',')),
  DEFAULT_MARKET: z.string(),
  NEXT_PUBLIC_BACKEND_URL: z.string().url(),
  NEXT_PUBLIC_S3_URL: z.string().url(),
  VERBOSE_LOGGING: z
    .string()
    .optional()
    .transform(val => val === 'true' || val === '1'),
  AUTH_SECRET: z.string(),
})

const clientEnvSchema = z.object({
  NEXT_PUBLIC_APP_NAME: z.string(),
  NEXT_PUBLIC_BACKEND_URL: z.string().url(),
  NEXT_PUBLIC_S3_URL: z.string().url(),
})

const vars = {
  NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
  SUPPORTED_MARKETS: process.env.SUPPORTED_MARKETS,
  DEFAULT_MARKET: process.env.DEFAULT_MARKET,
  NEXT_PUBLIC_BACKEND_URL: process.env.NEXT_PUBLIC_BACKEND_URL,
  NEXT_PUBLIC_S3_URL: process.env.NEXT_PUBLIC_S3_URL,
  VERBOSE_LOGGING: process.env.VERBOSE_LOGGING,
  AUTH_SECRET: process.env.AUTH_SECRET,
}

// Parse and validate the environment variables
export const envVars = (typeof window === 'undefined' ? serverEnvSchema : clientEnvSchema).parse(vars) as z.infer<
  typeof serverEnvSchema
>
