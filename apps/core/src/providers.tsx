import { SessionProvider } from 'next-auth/react'
import { auth } from '@/auth'
import { ClientProviders } from '@/client-providers'
import { AuthProvider } from '@/modules/auth/auth.provider'

export async function Providers({ children }: { children: React.ReactNode }) {
  const session = await auth()

  return (
    <SessionProvider refetchOnWindowFocus={false} session={session}>
      <AuthProvider session={session}>
        <ClientProviders>{children}</ClientProviders>
      </AuthProvider>
    </SessionProvider>
  )
}
