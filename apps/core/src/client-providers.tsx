'use client'
import { useRouter } from 'next/navigation'
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { MotionProvider } from '@/providers/motion-provider'
import ReduxStoreProvider from '@/store/ReduxStoreProvider'
import { HeroUIProvider } from '@heroui/system'
import { ToastProvider } from '@heroui/toast'

export function ClientProviders({ children }: { children: React.ReactNode }) {
  const router = useRouter()

  return (
    <HeroUIProvider navigate={router.push}>
      <NextThemesProvider attribute="class">
        <ToastProvider />
        <MotionProvider>
          <ReduxStoreProvider>{children}</ReduxStoreProvider>
        </MotionProvider>
      </NextThemesProvider>
    </HeroUIProvider>
  )
}
