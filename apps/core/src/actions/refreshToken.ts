'use server'

import { unstable_update } from '@/auth'
import { envVars } from '@/env'
import type { IAuthState } from '@/modules/auth/auth.store'
import type { IServerActionResult } from '@/types/serverActions'

export const refreshTokenAction = async (refreshToken: string): Promise<IServerActionResult<IAuthState>> => {
  try {
    if (!refreshToken) {
      throw new Error('No refresh token found')
    }

    const response = await fetch(`${envVars.NEXT_PUBLIC_BACKEND_URL}/auth/token/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${refreshToken}`,
      },
    })

    if (!response.ok) {
      throw new Error('Failed to refresh token')
    }

    const data = (await response.json()).data
    if (!data || !data.accessToken) {
      throw new Error('No new accessToken received')
    }
    await unstable_update({ accessToken: data.accessToken, refreshToken: data.refreshToken })

    return {
      success: true,
      data,
    }
  } catch (error: any) {
    console.error('Error in refreshTokenAction:', error)

    return {
      success: false,
      error: error.message || 'An unknown error occurred',
    }
  }
}
