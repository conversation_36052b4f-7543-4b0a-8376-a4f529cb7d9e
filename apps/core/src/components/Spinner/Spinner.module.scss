/* HTML: <div class="loader"></div> */

@use '@theme/functions.scss' as *;

@use '@theme/variables.scss' as *;

.loader {
  width: calculate-rem(50px);
  aspect-ratio: 1;
  border-radius: 50%;
  border: 8px solid $color-primary;
  border-right-color: $color-surface-1000;
  animation: l2 1s infinite linear;
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  position: relative;
  overflow: hidden;
}

.absolute {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: $color-background;
  z-index: 999;
}

@keyframes l2 {
  to {
    transform: rotate(1turn);
  }
}
