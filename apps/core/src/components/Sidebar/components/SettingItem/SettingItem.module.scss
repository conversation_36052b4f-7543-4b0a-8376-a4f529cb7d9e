@use './SettingItem.sd.module' as *;
@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  border-radius: calculate-rem(5px);
  align-content: center;
  padding: calculate-rem(16px);
  background-color: $color-secondary;
  color: $color-on-secondary;
  transition: background-color 0.2s ease;

  /* unclear what this should look like */
  &:hover {
    background-color: $color-primary;
  }
}
