import type { FC } from 'react'
import type { ISettingsSidebarSection } from '@repo/types/api/s3/sidebar-config'
import styles from '@components/Sidebar/components/SettingItem/SettingItem.module.scss'

type SettingItemProps = ISettingsSidebarSection['items'][0]

// TODO: Extend implementation
export const SettingItem: FC<SettingItemProps> = ({ type }) => {
  return (
    <div className={styles.container}>
      <span>{type}</span>
    </div>
  )
}
