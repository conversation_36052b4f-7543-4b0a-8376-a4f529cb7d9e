import { getSidebarConfig } from '@/network/server-utils/s3/getters'
import { authService } from '@/services/auth.service'
import { RHINOLAYER_TARGET_VALUES } from '@repo/constants/rhinoLayer'
import type { IMainNavigationData } from '@repo/types/api/s3/sidebar-config'

export async function getMainNavigationData(): Promise<IMainNavigationData> {
  const loggedIn = await authService.isAuthenticated()

  try {
    const config = await getSidebarConfig()
    const sections = config?.left?.sections || []

    // Filter sections based on authentication status
    const filteredSections = sections.filter(
      section =>
        !(
          (section.hide_for?.auth === RHINOLAYER_TARGET_VALUES.LOGGED_OUT && !loggedIn) ||
          (section.hide_for?.auth === RHINOLAYER_TARGET_VALUES.LOGGED_IN && loggedIn)
        ),
    )

    return { sections: filteredSections }
  } catch (error) {
    console.error('Error fetching main navigation data:', error)
    return {
      sections: undefined,
      error: error instanceof Error ? error.message : 'Failed to load navigation data',
    }
  }
}
