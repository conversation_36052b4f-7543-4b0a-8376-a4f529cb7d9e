'use client'
import { SidebarStateWrapper } from '@components/Sidebar/components/shared/SidebarStates'
import { SidebarSections } from '@components/Sidebar/SidebarSections'
import type { SidebarSection } from '@repo/types/api/s3/sidebar-config'

interface IMainNavigationProps {
  sections?: SidebarSection[]
  error?: string
  isLoading?: boolean
}

export const MainNavigationClient = ({ sections, error, isLoading }: IMainNavigationProps) => {
  const isEmpty = Boolean(!sections || sections.length === 0)
  const showEmpty = isEmpty && !isLoading && !error

  return (
    <SidebarStateWrapper
      title="Navigation"
      isLoading={Boolean(isLoading)}
      error={error}
      isEmpty={showEmpty}
      loadingMessage="Loading navigation..."
      emptyMessage="No navigation items available">
      <SidebarSections sections={sections || []} />
    </SidebarStateWrapper>
  )
}
