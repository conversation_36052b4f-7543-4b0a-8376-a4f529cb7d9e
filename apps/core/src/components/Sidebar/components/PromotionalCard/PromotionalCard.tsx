import type { FC } from 'react'
import Image from 'next/image'
import type { IPromotionalCardsSidebarSection } from '@repo/types/api/s3/sidebar-config'
import styles from '@components/Sidebar/components/PromotionalCard/PromotionalCard.module.scss'

type PromotionalCardProps = IPromotionalCardsSidebarSection['items'][0]

// Would probably have to add another layer of abstraction
// to support different types of promotional cards and fetch them accordingly
// so we can get the number of items available per type
// which probably should be the value for the badge which is currently hardcoded to 1
export const PromotionalCard: FC<PromotionalCardProps> = ({ title, thumbnail }) => {
  return (
    <div className={styles.container}>
      {/* TODO: Fix background image refetching on hover causing blur */}
      <Image src={thumbnail} alt="Promo Card" fill className={styles.backgroundImage} sizes="100" priority />
      <span className={styles.badge}>1</span>
      <span className={styles.headline}>{title}</span>
    </div>
  )
}
