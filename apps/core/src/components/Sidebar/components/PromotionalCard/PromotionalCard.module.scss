@use './PromotionalCard.sd.module' as *;
@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 56px;
  border-radius: calculate-rem(12px);
  overflow: hidden;
  transition: transform 0.5s ease;
}

.container:hover {
  cursor: pointer;
  transform: scale(1.02);
}

.backgroundImage {
  object-fit: cover;
  border-radius: calculate-rem(12px);
}

.badge {
  $badge-size: 18px;

  position: absolute;
  top: calculate-rem(10px);
  left: calculate-rem(10px);
  background-color: $color-primary;
  height: calculate-rem(18px);
  width: calculate-rem(18px);
  border-radius: $badge-size;
  line-height: $badge-size;
  color: $color-on-primary;
  font-weight: 700;
  font-size: calculate-rem(11px);
  text-align: center;
}

.headline {
  position: absolute;
  left: calculate-rem(10px);
  bottom: calculate-rem(10px);
  color: $color-on-tertaiary;
  font-weight: 700;
  font-size: calculate-rem(14px);
  line-height: 1;
  z-index: 1;
}
