import type { FC } from 'react'
import { NavigationLink } from '@components/Sidebar/components/NavigationLink/NavigationLink'
import type { INavigationLinksSidebarSection } from '@repo/types/api/s3/sidebar-config'
import { SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarMenu } from '@repo/ui/shadcn/sidebar'

type NavigationLinksSidebarSectionProps = INavigationLinksSidebarSection

export const NavigationLinksSidebarSection: FC<NavigationLinksSidebarSectionProps> = ({ items: links, title }) => {
  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {links.map((link, i) => (
            <NavigationLink key={i} {...link} />
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
