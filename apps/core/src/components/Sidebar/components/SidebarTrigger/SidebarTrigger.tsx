'use client'

import { useCallback } from 'react'
import { PanelLeftIcon, PanelRightIcon } from 'lucide-react'
import type { SidebarSide } from '@components/Sidebar/SidebarContentComponents'
import { SidebarSideEnum } from '@repo/constants/sidebar'
import { Button } from '@repo/ui/shadcn/button'
import { useSidebar } from '@repo/ui/shadcn/sidebar'

interface ISidebarTriggerProps {
  side: SidebarSide
}

export const SidebarTrigger = ({ side }: ISidebarTriggerProps) => {
  const { isOpen, setIsOpen } = useSidebar(side)

  const onToggleButtonClick = useCallback(() => {
    setIsOpen(!isOpen)
  }, [isOpen, setIsOpen])

  const Icon = side === SidebarSideEnum.LEFT ? PanelLeftIcon : PanelRightIcon

  return (
    <Button
      data-sidebar="trigger"
      data-slot="sidebar-trigger"
      variant="ghost"
      size="icon"
      className="size-7"
      onClick={onToggleButtonClick}>
      <Icon />
      <span className="sr-only">Toggle {side === SidebarSideEnum.LEFT ? 'Left' : 'Right'} Sidebar</span>
    </Button>
  )
}
