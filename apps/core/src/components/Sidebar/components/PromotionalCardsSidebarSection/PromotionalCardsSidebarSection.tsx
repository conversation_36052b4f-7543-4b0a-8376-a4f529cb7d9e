import type { FC } from 'react'
import { PromotionalCard } from '@components/Sidebar/components/PromotionalCard/PromotionalCard'
import type { IPromotionalCardsSidebarSection } from '@repo/types/api/s3/sidebar-config'
import styles from '@components/Sidebar/components/PromotionalCardsSidebarSection/PromotionalCardsSidebarSection.module.scss'

type PromotionalCardsSidebarSectionProps = IPromotionalCardsSidebarSection

export const PromotionalCardsSidebarSection: FC<PromotionalCardsSidebarSectionProps> = ({ items: cards }) => {
  return (
    <div className={styles.container}>
      {cards.map((card, i) => (
        <div key={i} className={styles.card}>
          <PromotionalCard {...card} />
        </div>
      ))}
    </div>
  )
}
