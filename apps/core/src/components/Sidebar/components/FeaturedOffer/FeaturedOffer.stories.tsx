import { FeaturedOffer } from '@components/Sidebar/components/FeaturedOffer/FeaturedOffer'
import type { IFeaturedOfferSidebarSection } from '@repo/types/api/s3/sidebar-config'
import type { Meta, StoryObj } from '@storybook/react'

type FeaturedOfferDisplayProps = Omit<IFeaturedOfferSidebarSection, 'type'>

const FeaturedOfferStoryWrapper = (props: FeaturedOfferDisplayProps) => {
  return <FeaturedOffer {...props} />
}

const meta: Meta<typeof FeaturedOfferStoryWrapper> = {
  title: 'Sidebar/FeaturedOffer',
  component: FeaturedOfferStoryWrapper,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A featured offer component for the sidebar that supports multiple states: initial (with CTA button), ' +
          'countdown (after claiming), and expired. Features Next.js optimized background images, responsive layout, ' +
          'and self-contained claim logic with optional post-claim callbacks.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    id: 1,
    headline: 'Daily Bonus',
    subline: 'Make first purchase and receive free spins!',
    ctaTitle: 'Claim',
    backgroundImage: 'https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg',
    hasIllustration: false,
  },
  argTypes: {
    id: {
      control: 'number',
      description: 'Unique identifier for the offer',
    },
    headline: {
      control: 'text',
      description: 'Main headline text displayed on the card',
    },
    subline: {
      control: 'text',
      description: 'Supporting text/description displayed below headline',
    },
    ctaTitle: {
      control: 'text',
      description: 'Call-to-action button text for claiming the offer',
    },
    backgroundImage: {
      control: 'text',
      description: 'Background image URL - optimized with Next.js Image component',
    },
    hasIllustration: {
      control: 'boolean',
      description: 'Toggle between illustration layout (column) and compact layout (row)',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '350px',
          minHeight: '180px',
          background: '#f5f5f5',
          padding: '20px',
          borderRadius: '8px',
          position: 'relative',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    id: { table: { disable: true } },
    headline: { table: { disable: true } },
    subline: { table: { disable: true } },
    ctaTitle: { table: { disable: true } },
    backgroundImage: { table: { disable: true } },
    hasIllustration: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'All featured offer variants, layouts, and content variations',
      },
    },
  },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Layout Variants</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
              Compact Layout (No Illustration)
            </h4>
            <div
              style={{
                width: '350px',
                minHeight: '180px',
                background: '#f5f5f5',
                padding: '20px',
                borderRadius: '8px',
                position: 'relative',
              }}>
              <FeaturedOffer
                id={1}
                headline="Daily Bonus"
                subline="Make first purchase and receive free spins!"
                ctaTitle="Claim"
                backgroundImage="https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg"
                hasIllustration={false}
              />
            </div>
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
              Illustration Layout (Column)
            </h4>
            <div
              style={{
                width: '350px',
                minHeight: '180px',
                background: '#f5f5f5',
                padding: '20px',
                borderRadius: '8px',
                position: 'relative',
              }}>
              <FeaturedOffer
                id={2}
                headline="Daily Bonus"
                subline="Make first purchase and receive free spins!"
                ctaTitle="Claim"
                backgroundImage="https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg"
                hasIllustration={true}
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Content Variations</h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Long Content Text</h4>
            <div
              style={{
                width: '350px',
                minHeight: '180px',
                background: '#f5f5f5',
                padding: '20px',
                borderRadius: '8px',
                position: 'relative',
              }}>
              <FeaturedOffer
                id={3}
                headline="Premium Weekend Special"
                subline="This is a longer description that shows how the component handles more content with proper wrapping."
                ctaTitle="Activate Premium"
                backgroundImage="https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg"
                hasIllustration={true}
              />
            </div>
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Different CTA Text</h4>
            <div
              style={{
                width: '350px',
                minHeight: '180px',
                background: '#f5f5f5',
                padding: '20px',
                borderRadius: '8px',
                position: 'relative',
              }}>
              <FeaturedOffer
                id={4}
                headline="VIP Bonus"
                subline="Exclusive offer for VIP members with amazing rewards!"
                ctaTitle="Unlock VIP Benefits"
                backgroundImage="https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg"
                hasIllustration={false}
              />
            </div>
          </div>

          <div>
            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Custom Background Image</h4>
            <div
              style={{
                width: '350px',
                minHeight: '180px',
                background: '#f5f5f5',
                padding: '20px',
                borderRadius: '8px',
                position: 'relative',
              }}>
              <FeaturedOffer
                id={5}
                headline="Special Offer"
                subline="Limited time bonus available now!"
                ctaTitle="Get Bonus"
                backgroundImage="https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop"
                hasIllustration={false}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive featured offer with all properties available for customization',
      },
    },
  },
  args: {
    id: 1,
    headline: 'Interactive Offer',
    subline: 'Customize all properties to see how the component responds!',
    ctaTitle: 'Claim Now',
    backgroundImage: 'https://luckyspins-staging4.imgix.net/icons/sweeps/featured-offer-background.jpg',
    hasIllustration: false,
  },
  render: args => (
    <div
      style={{
        width: '350px',
        minHeight: '180px',
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <FeaturedOffer {...args} />
    </div>
  ),
}
