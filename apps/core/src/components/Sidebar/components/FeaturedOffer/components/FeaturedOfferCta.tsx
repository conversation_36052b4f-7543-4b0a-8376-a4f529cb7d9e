'use client'
import type { FC } from 'react'
import type { IButtonProps } from '@components/Button'
import { Button } from '@components/Button'

interface IFeaturedOfferCtaProps extends Omit<IButtonProps, 'label' | 'onClick'> {
  offerId: number
  ctaTitle: string
  onAfterClaim?: () => void | Promise<void>
}

export const FeaturedOfferCta: FC<IFeaturedOfferCtaProps> = ({ offerId, ctaTitle, onAfterClaim, ...props }) => {
  const handleClick = async () => {
    try {
      // TODO: Replace with actual API call to claim the offer
      onAfterClaim?.()
    } catch (error) {
      console.error('Error claiming offer:', error)
    }
  }

  return <Button color="secondary" label={ctaTitle} onClick={handleClick} {...props} />
}
