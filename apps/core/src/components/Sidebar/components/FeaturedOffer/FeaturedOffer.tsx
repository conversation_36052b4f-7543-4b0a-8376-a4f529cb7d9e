'use client'
import { useCallback, useMemo, useState } from 'react'
import type { FC } from 'react'
import clsx from 'clsx'
import Image from 'next/image'
import { CountdownTimer } from '@components/CountdownTimer/CountdownTimer'
import { FeaturedOfferCta } from '@components/Sidebar/components/FeaturedOffer/components/FeaturedOfferCta'
import { FeaturedOfferStates } from '@repo/constants/sidebar'
import type { IFeaturedOfferSidebarSection } from '@repo/types/api/s3/sidebar-config'
import styles from '@components/Sidebar/components/FeaturedOffer/FeaturedOffer.module.scss'

type FeaturedOfferProps = Omit<IFeaturedOfferSidebarSection, 'type'>

export const FeaturedOffer: FC<FeaturedOfferProps> = props => {
  const [offerState, setOfferState] = useState<FeaturedOfferStates>(FeaturedOfferStates.INITIAL)
  const [countdownEndTime, setCountdownEndTime] = useState<Date | null>(null)

  // TODO: Use a selector to get the values from the config instead of passing props directly
  const { id, headline, subline, ctaTitle, backgroundImage, hasIllustration } = props

  const handleClaimClick = useCallback(async () => {
    try {
      // TODO: Call API to claim the offer
      const endTime = new Date()
      endTime.setHours(endTime.getHours() + 24)

      setCountdownEndTime(endTime)
      setOfferState(FeaturedOfferStates.COUNTDOWN)
    } catch (error) {
      console.error('Error claiming offer:', error)
    }
  }, [])

  const handleCountdownExpire = useCallback(() => {
    setOfferState(FeaturedOfferStates.EXPIRED)
  }, [])

  const contentData = useMemo(() => {
    switch (offerState) {
      case FeaturedOfferStates.INITIAL:
      case FeaturedOfferStates.COUNTDOWN:
        return { headline, subline }
      case FeaturedOfferStates.EXPIRED:
        return {
          headline: 'Offer Expired',
          subline: 'This offer has expired. Check back later for new offers!',
        }
      default:
        return { headline: '', subline: '' }
    }
  }, [offerState, headline, subline])

  const actionContent = useMemo(() => {
    switch (offerState) {
      case FeaturedOfferStates.INITIAL:
        return (
          <div className={styles.ctaSection}>
            <FeaturedOfferCta offerId={id} ctaTitle={ctaTitle} onAfterClaim={handleClaimClick} />
          </div>
        )
      case FeaturedOfferStates.COUNTDOWN:
        return countdownEndTime ? (
          <div className={styles.countdownSection}>
            <CountdownTimer endTime={countdownEndTime} onExpire={handleCountdownExpire} className={styles.timer} />
          </div>
        ) : null
      case FeaturedOfferStates.EXPIRED:
      default:
        return null
    }
  }, [offerState, id, ctaTitle, countdownEndTime, handleClaimClick, handleCountdownExpire])

  return (
    <div className={styles.container}>
      <Image
        src={backgroundImage}
        loader={({ src, width }) => src + `?dpr=2&w=${width}&q=95`}
        className={styles.featuredOfferBackground}
        quality={95}
        fill
        alt="card-background"
        sizes="15vw"
        style={{
          objectFit: 'cover',
        }}
      />
      <div
        className={clsx(
          styles.content,
          hasIllustration ? styles.featuredOffer : styles.featuredOfferWithoutIllustration,
        )}>
        <div className={styles.textContent}>
          <h3 className={styles.headline}>{contentData.headline}</h3>
          <p className={styles.subline}>{contentData.subline}</p>
        </div>
        {actionContent}
      </div>
    </div>
  )
}
