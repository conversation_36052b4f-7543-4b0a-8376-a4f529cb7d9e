import type { FC } from 'react'
import { SettingItem } from '@components/Sidebar/components/SettingItem/SettingItem'
import type { ISettingsSidebarSection } from '@repo/types/api/s3/sidebar-config'
import styles from '@components/Sidebar/components/SettingsSidebarSection/SettingsSidebarSection.module.scss'

type SettingsSidebarSectionProps = ISettingsSidebarSection

export const SettingsSidebarSection: FC<SettingsSidebarSectionProps> = ({ items: settings }) => {
  return (
    <div className={styles.container}>
      {settings?.map((setting: ISettingsSidebarSection['items'][0], i) => (
        <SettingItem key={i} {...setting} />
      ))}
    </div>
  )
}
