'use client'

import React from 'react'
import { AlertCircleIcon, LoaderIcon, InfoIcon } from 'lucide-react'
import { FlexItemsWrapper } from '@components/Sidebar/components/shared/FlexItemsWrapper'

type SidebarStateType = 'loading' | 'error' | 'empty'

interface ISidebarStateProps {
  type: SidebarStateType
  title: string
  message?: string
  errorPrefix?: string
  className?: string
}

export const SidebarState = ({ type, title, message, errorPrefix = 'Error', className }: ISidebarStateProps) => {
  const containerClass = `p-4 ${className || ''}`

  const getContent = () => {
    switch (type) {
      case 'loading':
        return (
          <FlexItemsWrapper>
            <LoaderIcon className="h-4 w-4 animate-spin text-muted-foreground" />
            <p className="text-sm text-muted-foreground">{message || 'Loading...'}</p>
          </FlexItemsWrapper>
        )
      case 'error':
        return (
          <div className="space-y-2">
            <FlexItemsWrapper>
              <AlertCircleIcon className="h-4 w-4 text-red-500" />
              <p className="text-sm">Content unavailable</p>
            </FlexItemsWrapper>
            {!!message && (
              <p className="text-red-500 text-xs">
                {errorPrefix}: {message}
              </p>
            )}
          </div>
        )
      case 'empty':
        return (
          <FlexItemsWrapper>
            <InfoIcon className="h-4 w-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">{message || 'No items available'}</p>
          </FlexItemsWrapper>
        )
      default:
        return null
    }
  }

  return (
    <div className={containerClass}>
      <h3 className="font-medium mb-4">{title}</h3>
      {getContent()}
    </div>
  )
}

interface ISidebarStateWrapperProps {
  title: string
  isLoading?: boolean
  error?: string
  isEmpty?: boolean
  children: React.ReactNode
  loadingMessage?: string
  emptyMessage?: string
  errorPrefix?: string
  className?: string
}

export function SidebarStateWrapper({
  title,
  isLoading,
  error,
  isEmpty,
  children,
  loadingMessage,
  emptyMessage,
  errorPrefix,
  className,
}: ISidebarStateWrapperProps) {
  if (isLoading) {
    return <SidebarState type="loading" title={title} message={loadingMessage} className={className} />
  }

  if (error) {
    return <SidebarState type="error" title={title} message={error} errorPrefix={errorPrefix} className={className} />
  }

  if (isEmpty) {
    return <SidebarState type="empty" title={title} message={emptyMessage} className={className} />
  }

  return <>{children}</>
}
