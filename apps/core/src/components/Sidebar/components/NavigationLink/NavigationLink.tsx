'use client'
import type { FC } from 'react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { DynamicLink } from '@components/DynamicLink'
import type { INavigationLinksSidebarSection } from '@repo/types/api/s3/sidebar-config'
import { SidebarMenuItem, SidebarMenuButton } from '@repo/ui/shadcn/sidebar'
import styles from '@components/Sidebar/components/NavigationLink/NavigationLink.module.scss'

type NavigationLinkProps = INavigationLinksSidebarSection['items'][0]

export const NavigationLink: FC<NavigationLinkProps> = ({ title, icon, icon_dark, url }) => {
  const { resolvedTheme: theme } = useTheme()
  const isDarkMode = theme ? theme === 'dark' : true
  return (
    <SidebarMenuItem key={title}>
      <SidebarMenuButton asChild>
        <DynamicLink href={url} scroll={false}>
          {/* TODO: Fix theme mismatch, rm suppressHydrationWarning */}
          {/* Consider proper approach for rendering images according to theme */}
          <Image
            src={isDarkMode ? icon_dark : icon}
            alt="arrow"
            width={18}
            height={18}
            className={styles.icon}
            suppressHydrationWarning
          />
          <span className={styles.label}>{title}</span>
        </DynamicLink>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}
