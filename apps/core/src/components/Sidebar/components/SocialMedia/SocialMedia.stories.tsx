import { SocialMediaCard } from '@components/Sidebar/components/SocialMedia/SocialMedia'
import type { Meta, StoryObj } from '@storybook/nextjs'

const meta: Meta<typeof SocialMediaCard> = {
  title: 'Sidebar/SocialMediaCard',
  component: SocialMediaCard,
  tags: ['autodocs'],
  args: {
    title: 'Check us out on social media',
    backgroundPatternUrl: 'https://luckyspins-staging4.imgix.net/icons/sweeps/promo.png',
    items: [
      {
        name: 'facebook',
        url: 'https://facebook.com',
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
      },
      {
        name: 'twitter',
        url: 'https://twitter.com',
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
      },
      {
        name: 'instagram',
        url: 'https://instagram.com',
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
      },
      {
        name: 'youtube',
        url: 'https://youtube.com',
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
      },
    ],
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'Title shown above icons',
    },
    backgroundPatternUrl: {
      control: 'text',
      description: 'Background image URL',
    },
    items: {
      description: 'Array of social media icons with name, url, and icon',
    },
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          'Different display states of SocialMediaCard including full set, background-less, empty, and various icon counts.',
      },
    },
  },
  render: () => {
    const allItems = [
      {
        name: 'facebook',
        url: 'https://facebook.com',
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
      },
      {
        name: 'twitter',
        url: 'https://twitter.com',
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
      },
      {
        name: 'instagram',
        url: 'https://instagram.com',
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
      },
      {
        name: 'youtube',
        url: 'https://youtube.com',
        icon: 'https://luckyspins4.imgix.net/icons/flash.svg',
        icon_dark: 'https://buustikasino.imgix.net/icons/instant-bonus.svg',
      },
    ]

    const bg = 'https://luckyspins-staging4.imgix.net/icons/sweeps/promo.png'

    return (
      <div
        style={{
          width: '300px',
          borderRadius: '12px',
          display: 'flex',
          flexDirection: 'column',
          gap: '1.5rem',
          padding: 16,
        }}>
        <SocialMediaCard id="variant-default" title="Default full set" backgroundPatternUrl={bg} items={allItems} />

        <SocialMediaCard id="variant-no-bg" title="No background image" items={allItems} />

        <SocialMediaCard id="variant-empty" title="Empty items" backgroundPatternUrl={bg} items={[]} />

        <SocialMediaCard
          id="variant-long-title"
          title="Very long title for layout testing in small components like this card that should not overflow."
          backgroundPatternUrl={bg}
          items={allItems}
        />

        <SocialMediaCard
          id="variant-three-icons"
          title="Three icons"
          backgroundPatternUrl={bg}
          items={allItems.slice(0, 3)}
        />

        <SocialMediaCard
          id="variant-two-icons"
          title="Two icons"
          backgroundPatternUrl={bg}
          items={allItems.slice(0, 2)}
        />

        <SocialMediaCard
          id="variant-one-icon"
          title="One icon"
          backgroundPatternUrl={bg}
          items={allItems.slice(0, 1)}
        />
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive playground to test different props on the SocialMediaCard.',
      },
    },
  },
  args: {
    title: 'Try customizing me!',
  },
  render: args => (
    <div style={{ maxWidth: 300, padding: 16, borderRadius: 12 }}>
      <SocialMediaCard {...args} />
    </div>
  ),
}
