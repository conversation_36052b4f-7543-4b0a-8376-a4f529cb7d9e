import Image from 'next/image'
import { useTheme } from 'next-themes'
import { But<PERSON> } from '@components/Button'
import { Card, CardHeader, CardTitle, CardContent } from '@components/Card'
import { DynamicLink } from '@components/DynamicLink'
import type { ISocialMediaCardsSidebarSection } from '@repo/types/api/s3/sidebar-config'
import styles from '@components/Sidebar/components/SocialMedia/SocialMedia.module.scss'

type SocialMediaProps = Omit<ISocialMediaCardsSidebarSection, 'type'>

export const SocialMediaCard = ({ title, backgroundPatternUrl, items }: SocialMediaProps) => {
  const { resolvedTheme: theme } = useTheme()
  const isDarkMode = theme ? theme === 'dark' : true

  return (
    <Card className={styles.socialMediaCard}>
      {backgroundPatternUrl ? (
        <Image
          src={backgroundPatternUrl}
          loader={({ src, width }) => src + `?dpr=2&w=${width}&q=95`}
          className={styles.backgroundImage}
          quality={95}
          fill
          alt="card-background"
          sizes="15vw"
          style={{
            objectFit: 'cover',
          }}
        />
      ) : null}

      <CardHeader className={styles.header}>
        <CardTitle as="h2" className={styles.title}>
          {title}
        </CardTitle>
      </CardHeader>

      <CardContent className={styles.content}>
        {items.map(({ icon, icon_dark, name, url }) => (
          <Button
            as={DynamicLink}
            key={name}
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={name}
            color="overlay"
            size="sm"
            icon={isDarkMode ? icon_dark : icon}
          />
        ))}
      </CardContent>
    </Card>
  )
}
