'use client'
import React, { useState } from 'react'
import { SidebarStateWrapper } from '@components/Sidebar/components/shared/SidebarStates'

interface IChatMessage {
  id: string
  content: string
  timestamp: Date
  isUser?: boolean
}

interface IChatData {
  messages?: Array<IChatMessage>
  error?: string
  isLoading?: boolean
}

export function ChatClient({ messages: initialMessages = [], error, isLoading }: IChatData) {
  const [messages, setMessages] = useState<IChatMessage[]>(initialMessages)
  const [inputValue, setInputValue] = useState('')

  const isEmpty = Boolean(messages.length === 0)

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim()) return

    const newMessage: IChatMessage = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, newMessage])
    setInputValue('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(e)
    }
  }

  return (
    <SidebarStateWrapper
      title="Chat"
      isLoading={Boolean(isLoading)}
      error={error}
      isEmpty={Boolean(isEmpty && !isLoading && !error)}
      loadingMessage="Loading chat..."
      emptyMessage="No messages yet">
      <div className="flex flex-col h-full">
        <div className="p-4 pb-2">
          <h3 className="font-medium mb-4">Chat</h3>
        </div>

        {/* Messages container */}
        <div className="flex-1 overflow-y-auto px-4 space-y-4">
          {messages.map((message, index) => {
            const isAlternate = index % 2 === 0
            return (
              <div key={message.id} className="flex justify-start">
                <div className="flex items-start gap-3 w-full max-w-[90%]">
                  <div
                    className={
                      'w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 ' +
                      (isAlternate ? 'bg-gray-700 text-white' : 'bg-gray-600 text-white')
                    }>
                    {isAlternate ? 'A' : 'O'}
                  </div>

                  <div
                    className={`p-3 rounded-lg text-sm min-w-0 flex-1 ${
                      isAlternate ? 'bg-gray-800 text-white rounded-tl-sm' : 'bg-gray-700 text-white'
                    }`}>
                    <div className="break-words">{message.content}</div>
                    <div className="text-xs mt-2 text-gray-300">
                      {message.timestamp.toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        <div className="border-t pt-3 border-gray-200">
          <form onSubmit={handleSendMessage} className="flex gap-2 w-full">
            <input
              type="text"
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Type your message..."
              className={
                'flex-1 p-2 border border-gray-300 rounded-lg text-sm ' +
                'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              }
              disabled={isLoading}
            />
            <button
              type="submit"
              disabled={!inputValue.trim() || isLoading}
              className={
                'px-3 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium ' +
                'hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
              }>
              Send
            </button>
          </form>
        </div>
      </div>
    </SidebarStateWrapper>
  )
}
