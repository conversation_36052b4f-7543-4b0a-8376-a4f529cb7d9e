import type React from 'react'
import { Chat } from '@components/Sidebar/components/Chat/Chat'
import { MainNavigation } from '@components/Sidebar/components/MainNavigation'
import type { SidebarSideEnum } from '@repo/constants/sidebar'
import type { SidebarContentType } from '@repo/types/api/s3/sidebar-config'

export type SidebarComponentMap = Record<SidebarContentType, () => Promise<React.JSX.Element>>
export type SidebarSide = SidebarSideEnum

export const sidebarComponents: SidebarComponentMap = {
  'main-navigation': MainNavigation,
  chat: Chat,
} as const

export function getSidebarComponent(contentType: SidebarContentType): (() => Promise<React.JSX.Element>) | undefined {
  return sidebarComponents[contentType]
}
