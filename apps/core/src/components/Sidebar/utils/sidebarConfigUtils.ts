import { getSidebarConfig } from '@/network/server-utils/s3/getters'
import { SidebarSideEnum } from '@repo/constants/sidebar'
import type { SidebarContentType } from '@repo/types/api/s3/sidebar-config'

function isValidSidebarContentType(type: string): type is SidebarContentType {
  return type === 'main-navigation' || type === 'chat'
}

const DEFAULT_CONTENT_TYPES = {
  [SidebarSideEnum.LEFT]: 'main-navigation',
  [SidebarSideEnum.RIGHT]: 'chat',
} as const

/**
 * Generic function to get sidebar content type for any side
 * @param side - The sidebar side ('left' or 'right')
 * @returns Promise<SidebarContentType> - The content type for the specified sidebar
 */
export async function getSidebarContentType(side: 'left' | 'right'): Promise<SidebarContentType> {
  try {
    const config = await getSidebarConfig()
    const configType = config?.[side]?.type
    const defaultType = DEFAULT_CONTENT_TYPES[side]

    if (configType && isValidSidebarContentType(configType)) {
      return configType
    }

    return defaultType
  } catch (error) {
    console.error(`Error fetching ${side} sidebar config:`, error)
    return DEFAULT_CONTENT_TYPES[side]
  }
}
