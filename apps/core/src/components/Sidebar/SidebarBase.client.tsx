'use client'
import React from 'react'
import { Sidebar } from '@components/Sidebar'
import type { SidebarSide } from '@components/Sidebar/SidebarContentComponents'
import { SidebarSideEnum } from '@repo/constants/sidebar'
import { useSidebar } from '@repo/ui/shadcn/sidebar'

interface ISidebarBaseClientProps {
  side: SidebarSide
  children: React.ReactNode
}

export const SidebarBaseClient = ({ side, children }: ISidebarBaseClientProps) => {
  const { isOpen, setIsOpen } = useSidebar(side)

  const sideConstant = side === SidebarSideEnum.LEFT ? SidebarSideEnum.LEFT : SidebarSideEnum.RIGHT

  return <Sidebar side={sideConstant} isOpen={isOpen} setIsOpen={setIsOpen} content={children} />
}
