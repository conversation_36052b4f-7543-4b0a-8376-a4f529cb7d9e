import type { FC } from 'react'
import { FeaturedOffer } from '@components/Sidebar/components/FeaturedOffer/FeaturedOffer'
import { NavigationLinksSidebarSection } from '@components/Sidebar/components/NavigationLinksSidebarSection/NavigationLinksSidebarSection'
import { PromotionalCardsSidebarSection } from '@components/Sidebar/components/PromotionalCardsSidebarSection/PromotionalCardsSidebarSection'
import { SettingsSidebarSection } from '@components/Sidebar/components/SettingsSidebarSection/SettingsSidebarSection'
import { SidebarDivider } from '@components/Sidebar/components/SidebarDivider/SidebarDivider'
import { SocialMediaCard as SocialMediaCardSection } from '@components/Sidebar/components/SocialMedia/SocialMedia'
import type { ISidebarConfig, SidebarSection } from '@repo/types/api/s3/sidebar-config'

const dashboardComponentMap: {
  [K in SidebarSection['type']]: React.FC<Extract<SidebarSection, { type: K }>>
} = {
  'promotional-cards': PromotionalCardsSidebarSection,
  'navigation-links': NavigationLinksSidebarSection,
  divider: SidebarDivider,
  'social-media': SocialMediaCardSection,
  'featured-offer': FeaturedOffer,
  settings: SettingsSidebarSection,
}

interface ISidebarSectionParserProps {
  section: SidebarSection
}

const SidebarSectionParser: FC<ISidebarSectionParserProps> = ({ section }) => {
  const Component = dashboardComponentMap[section.type] as React.FC<typeof section>

  if (!Component) {
    console.warn(`No component found for sidebar section type: ${section.type}`, section)
    return null
  }

  return <Component {...section} />
}

interface ISidebarSectionsProps {
  sections: ISidebarConfig['sections']
}

export const SidebarSections = ({ sections }: ISidebarSectionsProps) => {
  return sections.map((section, i) => <SidebarSectionParser key={i} section={section} />)
}
