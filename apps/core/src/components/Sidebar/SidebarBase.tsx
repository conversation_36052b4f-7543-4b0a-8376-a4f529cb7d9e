import React from 'react'
import { SidebarBaseClient } from '@components/Sidebar/SidebarBase.client'
import type { SidebarSide } from '@components/Sidebar/SidebarContentComponents'
import { getSidebarComponent } from '@components/Sidebar/SidebarContentComponents'
import { getSidebarContentType } from '@components/Sidebar/utils/sidebarConfigUtils'

interface ISidebarBaseProps {
  side: SidebarSide
}

export async function SidebarBase({ side }: ISidebarBaseProps) {
  const contentType = await getSidebarContentType(side)
  const Component = getSidebarComponent(contentType)

  if (!Component) {
    console.warn(`No component found for sidebar content type: ${contentType}`)
    return (
      <SidebarBaseClient side={side}>
        <div className="p-4">
          <h3 className="font-medium mb-4">Oops, seems like we can&apos;t find the content you are looking for</h3>
        </div>
      </SidebarBaseClient>
    )
  }

  return (
    <SidebarBaseClient side={side}>
      <Component />
    </SidebarBaseClient>
  )
}
