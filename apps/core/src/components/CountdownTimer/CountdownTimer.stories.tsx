import { CountdownTimer } from '@components/CountdownTimer/CountdownTimer'
import type { Meta, StoryObj } from '@storybook/react'

const meta: Meta<typeof CountdownTimer> = {
  title: 'CountdownTimer',
  component: CountdownTimer,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'Countdown timer component used in the FeaturedOffer after claiming. Shows days, hours, minutes, ' +
          'and seconds remaining with automatic expiration handling.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    endTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 12 * 60 * 60 * 1000 + 44 * 60 * 1000), // 2 days, 12 hours, 44 minutes from now
  },
  argTypes: {
    endTime: {
      control: 'date',
      description: 'The end time for the countdown',
    },
    onExpire: {
      action: 'expired',
      description: 'Callback function called when countdown reaches zero',
    },
    className: {
      control: 'text',
      description: 'Additional CSS class for styling',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '350px',
          height: '200px',
          padding: '20px',
          borderRadius: '16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof CountdownTimer>

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    endTime: { table: { disable: true } },
    onExpire: { table: { disable: true } },
    className: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'Different countdown timer scenarios showing various time durations',
      },
    },
  },
  render: () => {
    const now = new Date()

    // Different countdown scenarios
    const endTime1 = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000 + 12 * 60 * 60 * 1000 + 44 * 60 * 1000 + 9 * 1000)
    const endTime2 = new Date(now.getTime() + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 30 * 1000)
    const endTime3 = new Date(now.getTime() + 5 * 60 * 1000 + 15 * 1000)
    const endTime4 = new Date(now.getTime() + 45 * 1000)

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
        <div>
          <h3 style={{ marginBottom: '1rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Different Time Durations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Multi-day Countdown (2d 12h 44m 9s)
              </h4>
              <div
                style={{
                  background: '#f0f0f0',
                  padding: '20px',
                  borderRadius: '12px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '80px',
                }}>
                <CountdownTimer endTime={endTime1} />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>
                Almost a Day (23h 59m 30s)
              </h4>
              <div
                style={{
                  background: '#f0f0f0',
                  padding: '20px',
                  borderRadius: '12px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '80px',
                }}>
                <CountdownTimer endTime={endTime2} />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Few Minutes (5m 15s)</h4>
              <div
                style={{
                  background: '#f0f0f0',
                  padding: '20px',
                  borderRadius: '12px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '80px',
                }}>
                <CountdownTimer endTime={endTime3} />
              </div>
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>About to Expire (45s)</h4>
              <div
                style={{
                  background: '#fff3cd',
                  padding: '20px',
                  borderRadius: '12px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '80px',
                  border: '2px solid #ffeaa7',
                }}>
                <CountdownTimer endTime={endTime4} />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive countdown timer with customizable end time and event handling',
      },
    },
  },
  args: {
    endTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
  },
  render: args => (
    <div
      style={{
        background: 'lightgrey',
        padding: '40px',
        borderRadius: '16px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '120px',
      }}>
      <CountdownTimer {...args} />
    </div>
  ),
  argTypes: {
    endTime: { table: { disable: true } },
    onExpire: {
      action: 'countdown-expired',
      description: 'Fires when countdown reaches zero',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes for custom styling',
    },
  },
}
