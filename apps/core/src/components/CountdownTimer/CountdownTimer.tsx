'use client'
import { useState, useEffect } from 'react'
import type { FC } from 'react'
import clsx from 'clsx'
import { formatTimeValueWithLeadingZero, getTimeUnitLabel } from '@repo/helpers/timeHelpers'
import styles from '@components/CountdownTimer/CountdownTimer.module.scss'

export interface ICountdownTimerProps {
  endTime: Date
  onExpire?: () => void
  className?: string
}

interface ITimeLeft {
  days: number
  hours: number
  minutes: number
  seconds: number
}

export const CountdownTimer: FC<ICountdownTimerProps> = ({ endTime, onExpire, className }) => {
  const [timeLeft, setTimeLeft] = useState<ITimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 })

  useEffect(() => {
    const calculateTimeLeft = (): ITimeLeft => {
      const difference = endTime.getTime() - new Date().getTime()

      if (difference > 0) {
        return {
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60),
        }
      } else {
        onExpire?.()
        return { days: 0, hours: 0, minutes: 0, seconds: 0 }
      }
    }

    setTimeLeft(calculateTimeLeft())

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    return () => clearInterval(timer)
  }, [endTime, onExpire])

  return (
    <div className={styles.countdownContainer}>
      {Object.entries(timeLeft).map(([unit, value]) => (
        <div key={unit} className={clsx(styles.timeBlock, className)}>
          <div className={styles.timeNumber}>{formatTimeValueWithLeadingZero(value)}</div>
          <div className={styles.timeLabel}>{getTimeUnitLabel(unit)}</div>
        </div>
      ))}
    </div>
  )
}
