import Image from 'next/image'
import Link from 'next/link'
import { getWelcomeBanner } from '@/network/server-utils/getters'
import { sanitizeHtml } from '@/utils/sanitizeHtml'
import { getWelcomeOfferCardContent } from '@components/Banner/helpers'
import { DynamicLink } from '@components/DynamicLink'
import { Button } from '@heroui/button'
import styles from '@components/Banner/Banner.module.scss'

async function Banner({ market }: { market: string }) {
  const welcomeBanner = await getWelcomeBanner(market)
  const bannerItems = welcomeBanner?.bannerItems
  const bannerConfig = bannerItems ? getWelcomeOfferCardContent({ bannerItems, isBanner: true }) : null

  return (
    <div className={styles.banner}>
      {!!bannerConfig?.image && (
        // <ImgIx src={bannerConfig?.image} alt="Banner"
        // fill style={{ objectFit: 'cover' }} sizes="90vw" priority />
        <Image src={'/images/demo/banner.png'} alt="Banner" fill style={{ objectFit: 'cover' }} sizes="90vw" priority />
      )}
      <div className={styles.bannerOverlay}>
        <div className={styles.bannerContent}>
          <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(bannerConfig?.label) }} />
          <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(bannerConfig?.title) }} />
          <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(bannerConfig?.subtitle) }} />
          <Button
            href={bannerConfig?.button?.link || '#'}
            as={DynamicLink}
            LinkComponent={Link}
            color={bannerConfig?.button?.type === 'secondary' ? 'secondary' : 'primary'}>
            <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(bannerConfig?.button?.name) }} />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Banner
