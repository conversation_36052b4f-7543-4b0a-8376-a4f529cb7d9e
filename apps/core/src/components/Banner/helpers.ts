import { BannerComponentsEnum } from '@repo/constants/banners'
import type { IBannerItem, IRlBannerItem } from '@repo/types/api/rl/bannersContent'

type DeviceType = 'mobile' | 'app' | 'desktop' | 'all' | 'tablet'

function getBannerImagesByDevice(images: { device: DeviceType; src: string }[] = [], priorityDevices: DeviceType[]) {
  for (const device of priorityDevices) {
    const image = images.find(img => img.device === device)
    if (image) return image.src
  }
  return undefined
}

function getBannerComponents<T extends BannerComponentsEnum>(bannerItems: IBannerItem[] | undefined, type: T) {
  return bannerItems?.filter((item): item is IBannerItem<T> => item.type === type) || []
}

export const getWelcomeOfferCardContent = ({
  bannerItems,
  promotion,
  isBanner,
}: {
  bannerItems?: IRlBannerItem[]
  promotion?: unknown
  isBanner: boolean
}) => {
  if (!bannerItems && !promotion) {
    return null
  }

  if (isBanner) {
    const bannerItemComponentItems = bannerItems?.[0]?.items || []

    const bannerTextComponents = getBannerComponents<BannerComponentsEnum.TextComponent>(
      bannerItemComponentItems,
      BannerComponentsEnum.TextComponent,
    )
    const bannerImageListComponent = getBannerComponents<BannerComponentsEnum.ImageListComponent>(
      bannerItemComponentItems,
      BannerComponentsEnum.ImageListComponent,
    )

    const bannersTermsComponent = getBannerComponents<BannerComponentsEnum.TermsComponent>(
      bannerItemComponentItems,
      BannerComponentsEnum.TermsComponent,
    )

    const buttonComponent = getBannerComponents<BannerComponentsEnum.ButtonComponent>(
      bannerItemComponentItems,
      BannerComponentsEnum.ButtonComponent,
    )

    const bannerLabel = bannerTextComponents?.[0]?.content || ''
    const bannerTitle = bannerTextComponents?.[1]?.content || ''
    const bannerSubtitle = bannerTextComponents?.[2]?.content || ''
    const bannerTerms = bannersTermsComponent?.[0]?.content?.content
    const bannerImages = bannerImageListComponent?.[0]?.content?.images || []
    const bannerButton = buttonComponent?.[0]?.content
    const bannerImage = getBannerImagesByDevice(bannerImages, ['desktop', 'all'])

    return {
      label: bannerLabel,
      title: bannerTitle,
      subtitle: bannerSubtitle,
      image: bannerImage,
      terms: bannerTerms,
      button: bannerButton,
    }
  } else {
    // const promotionLabel = promotion?.caption || ''
    // const promotionTitle = promotion?.title || ''
    // const promotionImg = promotion?.image?.mobile
    // return {
    //   label: promotionLabel,
    //   title: promotionTitle,
    //   image: promotionImg,
    // }
  }
}
