'use client'
import React from 'react'
import { clsx } from 'clsx'
import type { LinkProps } from 'next/link'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { serverConfig } from '@/config/serverConfig'
import { removeTrailSlash } from '@repo/helpers/urlHelpers'
import styles from '@components/DynamicLink/DynamicLink.module.scss'

interface IDynamicLinkProps extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps>, LinkProps {
  children: React.ReactNode
  LinkComponent?: React.ElementType
  href: string
}

const DynamicLink: React.FC<IDynamicLinkProps> = ({
  href = '',
  LinkComponent = Link,
  children,
  className,
  ...props
}) => {
  const params = useParams()
  const market = params.market || serverConfig.defaultMarket
  const isExternalLink = href.startsWith('http') || href.startsWith('//')
  const formattedHref = href.startsWith('/') ? href : `/${href}`
  const dynamicHref = isExternalLink ? href : removeTrailSlash(`/${market}${formattedHref}`)

  return (
    <LinkComponent
      href={dynamicHref}
      target={isExternalLink ? '_blank' : undefined}
      rel={isExternalLink ? 'noopener noreferrer' : undefined}
      className={clsx(props.role !== 'button' && styles.link, className)}
      {...props}>
      {children}
    </LinkComponent>
  )
}

export default DynamicLink
