import React from 'react'
import { getAvailableGamesMap } from '@/network/server-utils/s3/getters/games'
import { GameTile } from '@components/GameTile'
import type { GetCasinoConfigResponse } from '@repo/types/api/s3/casino-config'
import type { RouteParams } from '@repo/types/nextjs'
import styles from '@components/GamesSection/GamesSection.module.scss'

type GamesSectionProps = RouteParams & {
  section: GetCasinoConfigResponse['sections'][0]
}

export const GamesSection = async ({ section, market }: GamesSectionProps) => {
  const sectionGames = section.games || []
  const gamesMap = await getAvailableGamesMap(market)

  if (!sectionGames.length) {
    console.warn(`No games found for section: ${section.name}`)
    return null
  }

  const desktopGamesLimit = section.desktopGamesLimit || 30
  const desktopRowCount = section.desktopRowCount || 1
  const mobileGamesLimit = section.mobileGamesLimit || 30
  const mobileRowCount = section.mobileRowCount || 1
  const totalGamesToShow = Math.max(desktopGamesLimit, mobileGamesLimit)

  const gamesToRender = sectionGames
    .map(gameId => gamesMap[gameId.toString()])
    .filter((game): game is NonNullable<typeof game> => Boolean(game))
    .slice(0, totalGamesToShow)

  const totalAvailableGames = gamesToRender.length

  if (!totalAvailableGames) {
    console.warn(`No available games found for section: ${section.name}`)
    return null
  }

  const gamesContainerStyle = {
    '--desktop-row-count': desktopRowCount,
    '--mobile-row-count': mobileRowCount,
  } as React.CSSProperties

  return (
    <div className={styles.container}>
      <h3>{section.name}</h3>
      <div className={styles.games} style={gamesContainerStyle}>
        {gamesToRender.map((game, index) => (
          <GameTile key={game.id} game={game} priority={index < 6} />
        ))}
      </div>
    </div>
  )
}

export default GamesSection
