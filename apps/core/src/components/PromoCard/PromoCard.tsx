'use client'
import type { FC } from 'react'
import { Calendar1Icon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@components/Button'
import type { IRlPromotion } from '@repo/types/api/rl/promotions'
import styles from '@components/PromoCard/PromoCard.module.scss'

interface IPromoCardProps {
  promotion?: IRlPromotion
}

const PromoCard: FC<IPromoCardProps> = ({ promotion, ...props }) => {
  if (!promotion) {
    return null
  }

  // Get the first CTA if available
  const ctaKeys = Object.keys(promotion.ctas || {})
  // Handle potential undefined with optional chaining
  const firstCta =
    ctaKeys.length > 0 && promotion.ctas ? promotion.ctas[ctaKeys[0] as keyof typeof promotion.ctas] : null

  // Format date to display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date)
  }

  // Calculate time remaining for countdown
  const calculateTimeRemaining = () => {
    const now = new Date()
    const endDate = promotion.expiresAt ? new Date(promotion.expiresAt) : null

    if (!endDate) return { days: 0, hours: 0, minutes: 0, seconds: 0 }

    const diff = endDate.getTime() - now.getTime()
    if (diff <= 0) return { days: 0, hours: 0, minutes: 0, seconds: 0 }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    return { days, hours, minutes, seconds }
  }

  // Get remaining time
  const timeRemaining = calculateTimeRemaining()

  const imageUrl = promotion.image?.desktop || promotion.image?.mobile || ''

  // Calendar icon SVG content for readability
  const calendarIcon = <Calendar1Icon />

  return (
    <div className={styles.container}>
      <div className={styles.imageContainer}>
        <Image
          src={imageUrl}
          alt={promotion.name || 'Promotion'}
          fill
          style={{ objectFit: 'cover' }}
          sizes="(max-width: 768px) 100vw, (min-width: 769px) 30vw"
          className={styles.image}
        />
        {!!promotion.isWelcomeOffer && <div className={styles.welcomeTag}>Welcome Offer</div>}
        {!!promotion.isOngoing && <div className={styles.ongoingTag}>Ongoing</div>}
      </div>

      <div className={styles.dateBar}>
        <div className={styles.calendarIcon}>{calendarIcon}</div>
        <div className={styles.date}>
          {promotion.startAt ? formatDate(promotion.startAt) : ''} -{' '}
          {promotion.expiresAt ? formatDate(promotion.expiresAt) : 'Ongoing'}
        </div>
      </div>

      <div className={styles.content}>
        <h3 className={styles.title}>{promotion.name}</h3>

        <div className={styles.description} dangerouslySetInnerHTML={{ __html: promotion.description }} />

        {!!promotion.expiresAt && (
          <div className={styles.countdown}>
            <div className={styles.countdownTitle}>Target Challenge</div>
            <div className={styles.countdownDescription}>{promotion.caption}</div>

            <div className={styles.countdownTimer}>
              <div className={styles.timeUnit}>
                <div className={styles.timeValue} suppressHydrationWarning>
                  {timeRemaining.days.toString().padStart(2, '0')}
                </div>
                <div className={styles.timeLabel}>d</div>
              </div>

              <div className={styles.timeUnit}>
                <div className={styles.timeValue} suppressHydrationWarning>
                  {timeRemaining.hours.toString().padStart(2, '0')}
                </div>
                <div className={styles.timeLabel}>h</div>
              </div>

              <div className={styles.timeUnit}>
                <div className={styles.timeValue} suppressHydrationWarning>
                  {timeRemaining.minutes.toString().padStart(2, '0')}
                </div>
                <div className={styles.timeLabel}>m</div>
              </div>

              <div className={styles.timeUnit}>
                <div className={styles.timeValue} suppressHydrationWarning>
                  {timeRemaining.seconds.toString().padStart(2, '0')}
                </div>
                <div className={styles.timeLabel}>s</div>
              </div>

              {!!firstCta && !!firstCta.body && (
                <div className={styles.learnMoreButton}>
                  <Button
                    label={firstCta.body.name.toUpperCase()}
                    href={firstCta.body.link}
                    color={(firstCta.body.type?.toLowerCase() as 'primary' | 'secondary' | 'tertiary') || 'primary'}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {!promotion.expiresAt && (
          <div className={styles.footer}>
            {!!firstCta && !!firstCta.body && (
              <div className={styles.ctaButton}>
                <Button
                  label={firstCta.body.name.toUpperCase()}
                  href={firstCta.body.link}
                  color={(firstCta.body.type?.toLowerCase() as 'primary' | 'secondary' | 'tertiary') || 'primary'}
                />
              </div>
            )}

            <Link href={`/promotions/${promotion.slug}`} className={styles.detailsLink}>
              View Details
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}

export default PromoCard
