@use './PromoCard.sd.module' as *;
@use '@theme/variables' as *;
@use '@theme/functions.scss' as *;

.container {
  position: relative;
  width: 100%;
  border-radius: calculate-rem(8px);
  overflow: hidden;
  background-color: $color-background;
  display: flex;
  flex-direction: column;
  margin-bottom: calculate-rem(16px);
}

.imageContainer {
  position: relative;
  width: 100%;
  height: calculate-rem(200px);
}

.image {
  border-radius: calculate-rem(8px) calculate-rem(8px) 0 0;
}

.welcomeTag,
.ongoingTag {
  position: absolute;
  top: calculate-rem(10px);
  padding: calculate-rem(4px) calculate-rem(8px);
  border-radius: calculate-rem(4px);
  font-size: calculate-rem(12px);
  font-weight: 600;
  z-index: 1;
}

.welcomeTag {
  left: calculate-rem(10px);
  background-color: $color-primary;
  color: $color-on-primary;
}

.ongoingTag {
  right: calculate-rem(10px);
  background-color: $color-primary;
}

.dateBar {
  display: flex;
  align-items: center;
  padding: calculate-rem(8px) calculate-rem(16px);
  background-color: $color-surface-100;
  font-size: calculate-rem(14px);
}

.calendarIcon {
  margin-right: calculate-rem(8px);
  display: flex;
  align-items: center;
}

.date {
  flex: 1;
}

.date {
  flex: 1;
}

.content {
  padding: calculate-rem(16px);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.title {
  font-size: calculate-rem(28px);
  font-weight: 700;
  margin: 0 0 calculate-rem(16px);
}

.description {
  font-size: calculate-rem(16px);
  line-height: 1.4;
  margin-bottom: calculate-rem(24px);
  flex-grow: 1;

  p {
    margin: 0;
  }
}

.countdown {
  margin-top: calculate-rem(16px);
}

.countdownTitle {
  font-size: calculate-rem(28px);
  font-weight: 700;
  margin-bottom: calculate-rem(12px);
}

.countdownDescription {
  font-size: calculate-rem(16px);
  margin-bottom: calculate-rem(24px);

  line-height: 1.4;
}

.countdownTimer {
  display: flex;
  align-items: flex-end;
  gap: calculate-rem(16px);
}

.timeUnit {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 3rem;
}

.timeValue {
  background-color: $color-primary;
  color: $color-on-primary;
  font-size: calculate-rem(19.2px);
  font-weight: 700;
  padding: calculate-rem(8px);
  border-radius: calculate-rem(4px);
  width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
}

.timeLabel {
  margin-top: calculate-rem(4px);
  font-size: calculate-rem(14px);
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.ctaButton,
.learnMoreButton {
  flex-grow: 1;
  margin-right: calculate-rem(8px);
  font-weight: 600;
  margin-left: auto;
}

.learnMoreButton {
  margin-left: calculate-rem(32px);
  white-space: nowrap;
}

.detailsLink {
  font-size: calculate-rem(14px);
  color: $color-primary;
  text-decoration: none;
  white-space: nowrap;

  &:hover {
    text-decoration: underline;
  }
}
