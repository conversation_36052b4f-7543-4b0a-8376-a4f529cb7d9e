'use client'
import React, { useState } from 'react'
import { clsx } from 'clsx'
import type { ImageProps } from 'next/image'
import Image from 'next/image'
import { Blurhash } from 'react-blurhash'
import styles from '@components/ImgIx/ImgIx.module.scss'

interface IImgIxServerClientProps extends ImageProps {
  blurhash?: string
  blurUrl?: string
}

/**
 * @description This component is used to render images using ImgIx service on the server side.
 * The ImgIx is used only to get an original asset URL,
 * the resize functionality is handled by the nextjs Image component
 */
const ImgIxServerClient = ({ src, blurhash, blurUrl, ...props }: IImgIxServerClientProps) => {
  const [error, setError] = useState(false)
  const [showBlurPlaceholder, setShowBlurPlaceholder] = useState(!!blurhash || !!blurUrl)
  const [imageLoaded, setImageLoaded] = useState(false)

  const handleImageLoad = () => {
    setImageLoaded(true)
    setTimeout(() => {
      setShowBlurPlaceholder(false)
    }, 300)
  }

  return (
    <div
      className={clsx(styles.container, styles.blurUrlContainer)}
      style={blurUrl && showBlurPlaceholder ? { backgroundImage: `url(${blurUrl})` } : undefined}>
      {!!showBlurPlaceholder && !!blurhash && (
        <div className={clsx(styles.blurhashContainer, styles.visible)}>
          <Blurhash hash={blurhash} width={props.width} height={props.height} className={styles.blurhash} />
        </div>
      )}
      <Image
        src={error ? '/images/fallback_dark.webp' : src}
        quality={95}
        loading="lazy"
        {...props}
        onError={_ => setError(true)}
        onLoad={handleImageLoad}
        className={clsx(styles.image, imageLoaded && styles.visible, props.className)}
        style={props.style}
      />
    </div>
  )
}

export default ImgIxServerClient
