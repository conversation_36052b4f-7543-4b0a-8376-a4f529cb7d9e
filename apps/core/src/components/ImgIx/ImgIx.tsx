import React from 'react'
import type { ImageProps } from 'next/image'
import ImgIxServerClient from '@components/ImgIx/ImgIx.client'
import { getImgIx, getImgIxBlurBase64, getImgIxBlurBlurhash, getImgIxBlurUrl } from '@components/ImgIx/server-utils'

type IImgIxServerProps = Omit<ImageProps, 'placeholder'> & {
  placeholder?: 'blurhash' | 'base64' | 'url'
}

/**
 * @description This component is used to render images using ImgIx service on the server side.
 * The ImgIx is used only to get an original asset URL,
 * the resize functionality is handled by the nextjs Image component
 */
const ImgIx = async ({
  src: baseSrc,
  placeholder = 'url', // base64 renders immediately, but increases page size,
  // blurhash renders after the client hydrated, but provides smaller page size
  ...props
}: IImgIxServerProps) => {
  let src = baseSrc
  let blurhash: string | undefined = undefined
  let blurBase64: string | undefined = undefined
  let blurUrl: string | undefined = undefined

  try {
    if (typeof baseSrc === 'string') {
      try {
        const imgIxSrc = await getImgIx(baseSrc, {
          w: props.width && props.unoptimized ? props.width : '1920',
          dpr: '2',
        })

        if (imgIxSrc) {
          src = imgIxSrc
        }
      } catch (error) {
        console.error('Error while getting ImgIx URL:', error)
        src = baseSrc
      }
    }

    if (placeholder === 'base64') {
      blurBase64 = await getImgIxBlurBase64(src as string)
    } else if (placeholder === 'blurhash') {
      blurhash = await getImgIxBlurBlurhash(src as string)
    } else if (placeholder === 'url') {
      blurUrl = await getImgIxBlurUrl(src as string)
    }
  } catch (error) {
    console.error('Unexpected error in ImgIx:', error)
    src = baseSrc
  }

  return (
    <>
      <ImgIxServerClient
        src={src}
        blurDataURL={blurBase64}
        placeholder={blurBase64 ? 'blur' : 'empty'}
        blurhash={blurhash}
        blurUrl={blurUrl}
        quality={95}
        {...props}
      />
    </>
  )
}

export default ImgIx
