import 'server-only'
import { unstable_cache } from 'next/cache'
import qs from 'query-string'
import { getImgIxConfig } from '@/network/server-utils/s3/getters'
import type { Keyable } from '@repo/types/common'

interface IResolveImgIxUrlParams {
  path: string
  middlewareImage: string | null
  middlewareImageHost: string | undefined
  imgIxParams: Keyable | undefined
}

// files types provided as it is by ImgIx
const extensionsWithNoParams = ['svg', 'gif']

function decorateImgIxUrlWithParams(path: string, params: Keyable = {}) {
  if (extensionsWithNoParams.some(ext => path?.includes(`.${ext}`))) {
    return path
  }

  let imgIxParams
  if (params) {
    imgIxParams = {
      fit: 'crop',
      auto: 'format',
      w: !params.h ? 1081 : undefined,
      ...params,
    }
  }

  return qs.stringifyUrl({
    url: path,
    query: imgIxParams,
  })
}

const resolveImgIxUrl = ({ path, middlewareImage, middlewareImageHost, imgIxParams }: IResolveImgIxUrlParams) => {
  if (middlewareImage !== null) {
    return middlewareImage
  }
  if (path.startsWith('http')) {
    return path
  }
  return decorateImgIxUrlWithParams(`${middlewareImageHost}${path}`, imgIxParams)
}

const Cache = new Map()

async function getMiddlewareImgIx(url: string, params?: Keyable) {
  if (!url) {
    return null
  }
  const imgIxConfig = await getImgIxConfig()
  const decodedPath = decodeURIComponent(url)

  const matchingImageConfig = imgIxConfig?.find(iic => decodedPath.startsWith(`${iic.imagesOrigin}`))
  if (!matchingImageConfig) {
    return null
  }
  const imgIxUrl = decodedPath.replace(matchingImageConfig.imagesOrigin, matchingImageConfig.imgIxHost)
  return decorateImgIxUrlWithParams(imgIxUrl, params)
}

export const getImgIx = unstable_cache(
  async (path: string, params?: Keyable) => {
    if (!path) {
      return undefined
    }

    const key = path + JSON.stringify(params)
    const cached = Cache.get(key)
    if (cached) {
      return cached
    }

    const isGif = path.endsWith('.gif')

    if (isGif) {
      return path // imgix returns only 1st frame for gif (static image)
    }

    const imgIxParams = {
      fit: params?.w && params?.h ? 'crop' : 'ar',
      auto: 'format',
      ...params,
    }

    return await getMiddlewareImgIx(path, imgIxParams)
  },
  [],
  { revalidate: 60 },
)

export const getImgIxBlurBase64 = unstable_cache(
  async (imgIxUrl: string) => {
    const blurUrl = await getImgIxBlurUrl(imgIxUrl)
    if (!blurUrl) {
      return undefined
    }

    try {
      const response = await fetch(blurUrl)
      if (response.ok) {
        const arrayBuffer = await response.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)
        const contentType = response.headers.get('content-type') || 'image/jpeg'
        return `data:${contentType};base64,${buffer.toString('base64')}`
      } else {
        console.log('Failed to fetch blur image:', response.status, response.statusText)
        return undefined
      }
    } catch (error) {
      console.error('Error fetching blur image:', error)
      return undefined
    }
  },
  [],
  { revalidate: 60 },
)

export const getImgIxBlurUrl = unstable_cache(
  async (imgIxUrl: string) => {
    if (typeof imgIxUrl !== 'string' || !imgIxUrl.includes('imgix')) {
      return
    }
    try {
      const url = new URL(imgIxUrl)
      url.searchParams.delete('auto')
      url.searchParams.set('w', '15')
      url.searchParams.set('blur', '20')
      url.searchParams.set('q', '50')

      return url.toString()
    } catch (error) {
      console.error('Error creating blur URL:', error)
      return undefined
    }
  },
  [],
  { revalidate: 60 },
)

export const getImgIxBlurBlurhash = unstable_cache(
  async (imgIxUrl: string) => {
    if (typeof imgIxUrl !== 'string' || !imgIxUrl.includes('imgix')) {
      return
    }
    try {
      const url = new URL(imgIxUrl)
      url.searchParams.delete('auto')
      url.searchParams.set('w', '32')
      url.searchParams.set('fm', 'blurhash')

      const response = await fetch(url.toString(), { next: { revalidate: 60 } })
      if (response.ok) {
        return response.text()
      } else {
        //  console.log('Failed to fetch blurhash image:', response.status, response.statusText)
        return undefined
      }
    } catch (error) {
      // console.error('Error fetching blurhash image:', error)
      return undefined
    }
  },
  [],
  { revalidate: 60 },
)
