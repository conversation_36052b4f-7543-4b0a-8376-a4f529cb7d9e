# Footer Configuration-Driven Rendering

## 🎯 How Configuration Properties Are Used

### **1. Layout Type (`layout.type`)**
```html
<footer data-layout-type="flex" data-layout-id="footer-layout-en-row">
```
- **CSS**: `.footer[data-layout-type="flex"]` applies flex layout
- **Purpose**: Different layout types can have different CSS behaviors

### **2. Section Enabled (`enabled`)**
```typescript
// Only enabled sections are rendered
const leftSections = LayoutConfigService.getSectionsByPosition(layoutConfig, 'left')
// This automatically filters by enabled: true
```

### **3. Section Order (`order`)**
```typescript
// Sections are sorted by order before rendering
static getEnabledSections(config: LayoutConfig): LayoutSection[] {
  return config.sections
    .filter(section => section.enabled)
    .sort((a, b) => a.order - b.order) // ← ORDER IS USED HERE
}
```

### **4. Section Position (`position`)**
```typescript
// Sections are grouped by position
const leftSections = LayoutConfigService.getSectionsByPosition(layoutConfig, 'left')
const centerSections = LayoutConfigService.getSectionsByPosition(layoutConfig, 'center')
const fullWidthSections = LayoutConfigService.getSectionsByPosition(layoutConfig, 'full-width')
```

### **5. Data Attributes for Debugging**
Each section gets configuration data attributes:
```html
<div 
  data-section-id="brand-section"
  data-section-type="brand" 
  data-section-order="1"
  data-section-position="left"
  data-section-enabled="true"
  data-section-index="0"
  class="footer-section footer-section--brand"
>
  <!-- Section content -->
</div>
```

### **6. CSS Custom Properties**
```css
:root {
  --footer-gap: 32px; /* From layout.spacing.gap */
  --section-count-left: 2; /* Number of left sections */
  --section-count-center: 1; /* Number of center sections */
  --section-order: 1; /* Individual section order */
}
```

## 🔍 Visual Debugging

In development, hover over any footer section to see:
- Section type
- Section order
- Section position
- Section enabled status

## 🎛️ Configuration Examples

### Change Section Order
```json
{
  "sections": [
    {"id": "social", "order": 1}, // Will appear first
    {"id": "brand", "order": 2}   // Will appear second
  ]
}
```

### Change Section Position
```json
{
  "sections": [
    {"id": "brand", "position": "center"}, // Moves to center
    {"id": "social", "position": "right"}  // Moves to right
  ]
}
```

### Disable Section
```json
{
  "sections": [
    {"id": "social", "enabled": false} // Won't render
  ]
}
```

## 🎯 Result

All configuration properties now directly affect the UI:
- **enabled** → Controls if section renders
- **order** → Controls visual stacking order  
- **position** → Controls which container section appears in
- **layout.type** → Controls overall footer layout behavior
- **layout.spacing.gap** → Controls spacing between sections
