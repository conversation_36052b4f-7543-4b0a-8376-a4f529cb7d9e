import React from 'react'
import { getAppRouter } from '@/services/router.service'
import { DynamicLink } from '@components/DynamicLink'
import styles from '@components/Header/Header.module.scss'

export const HeaderNavigation: React.FC = () => {
  return (
    <nav className={`${styles.nav} `}>
      <DynamicLink href={getAppRouter().casino} className={styles.navLink}>
        Casino
      </DynamicLink>
      <DynamicLink href={getAppRouter().liveCasino} className={styles.navLink}>
        Live Casino
      </DynamicLink>
    </nav>
  )
}
