'use client'
import React from 'react'
import Link from 'next/link'
import { useAppRouter } from '@/services/router.service'
import { DynamicLink } from '@components/DynamicLink'
import { HeaderNavigation, HeaderDebugInfo } from '@components/Header/components'
import { ThemeToggle } from '@components/ThemeToggle'
import { Button } from '@heroui/button'
import { NavbarContent } from '@heroui/navbar'

export const LoggedOutHeader: React.FC = () => {
  const appRouter = useAppRouter()

  return (
    <>
      <HeaderNavigation />
      <HeaderDebugInfo />
      <NavbarContent justify="end">
        <ThemeToggle />
        <Button as={DynamicLink} href={appRouter.login} LinkComponent={Link}>
          Login
        </Button>
        <Button as={DynamicLink} href={appRouter.register} LinkComponent={Link} color="secondary">
          Register
        </Button>
      </NavbarContent>
    </>
  )
}
