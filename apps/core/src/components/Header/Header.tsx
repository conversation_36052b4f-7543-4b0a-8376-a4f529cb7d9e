'use client'
import React from 'react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useAuthStore } from '@/modules/auth/auth.provider'
import { DynamicLink } from '@components/DynamicLink'
import { LoggedInHeader } from '@components/Header/logged-in/Header'
import { LoggedOutHeader } from '@components/Header/logged-out/Header'
import { SidebarTrigger } from '@components/Sidebar/components/SidebarTrigger/SidebarTrigger'
import { Navbar, NavbarBrand, NavbarContent } from '@heroui/navbar'
import { SidebarSideEnum } from '@repo/constants/sidebar'
import { useIsMounted } from '@repo/hooks/useIsMounted'
import { useIsMobile } from '@repo/hooks/useMobile'
import styles from '@components/Header/Header.module.scss'

const Header: React.FC<{ market: string }> = ({ market }) => {
  const status = useAuthStore(state => state.status)
  const isMobile = useIsMobile()
  const isMounted = useIsMounted()
  // TODO: Detect theme from <html> class
  const { resolvedTheme: theme } = useTheme()

  const logoSrc = theme === 'dark' ? '/assets/logo_dark.svg' : '/assets/logo_light.svg'

  let content
  if (status === 'authenticated') {
    content = <LoggedInHeader market={market} />
  } else if (status === 'unauthenticated') {
    content = <LoggedOutHeader />
  } else {
    content = <></>
  }

  return (
    <Navbar shouldHideOnScroll={isMobile} className={styles.container}>
      <NavbarBrand>
        <DynamicLink href="/" className={styles.logoLink}>
          {!!isMounted && <Image src={logoSrc || '/assets/logo_light.svg'} alt="logo" width={140} height={20} />}
        </DynamicLink>
        <SidebarTrigger side={SidebarSideEnum.LEFT} />
        <SidebarTrigger side={SidebarSideEnum.RIGHT} />
      </NavbarBrand>
      <NavbarContent className="hidden md:flex gap-4" justify="center">
        {content}
      </NavbarContent>
    </Navbar>
  )
}

export default Header
