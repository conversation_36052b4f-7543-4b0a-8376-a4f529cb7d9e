'use client'
import React from 'react'
import { useAuthStore } from '@/modules/auth/auth.provider'
import { HeaderNavigation, HeaderDebugInfo } from '@components/Header/components'
import { LogoutButton } from '@components/Header/logged-in/LogoutButton'
import { ThemeToggle } from '@components/ThemeToggle'
import { NavbarContent } from '@heroui/navbar'

export const LoggedInHeader: React.FC<{ market: string }> = ({ market }) => {
  const status = useAuthStore(state => state.status)

  return (
    <>
      <p>Logged in</p>
      <HeaderNavigation />
      <HeaderDebugInfo />
      <NavbarContent justify="end">
        <ThemeToggle />
        {status === 'authenticated' && <LogoutButton />}
      </NavbarContent>
    </>
  )
}
