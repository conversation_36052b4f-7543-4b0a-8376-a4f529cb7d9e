import { redirect } from 'next/navigation'
import { authService } from '@/services/auth.service'
import { getAppRouter } from '@/services/router.service'
import { Landing } from '@screens/home/<USER>/Landing/Landing'

export async function Home({ market }: { market: string }) {
  const loggedIn = await authService.isAuthenticated()

  if (loggedIn) {
    redirect(getAppRouter(market).casino)
  }

  return <Landing market={market} />
}
