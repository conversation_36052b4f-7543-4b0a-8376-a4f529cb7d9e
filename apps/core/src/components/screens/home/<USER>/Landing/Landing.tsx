import React from 'react'
import { getWelcomePageConfig } from '@/network/server-utils/s3/getters'
import { Banner } from '@components/Banner'
import { LandingGamesSection } from '@screens/home/<USER>/Landing/LandingGamesSection/LandingGamesSection'
import styles from '@screens/home/<USER>/Landing/Landing.module.scss'

interface ILandingProps {
  market: string
}

export const Landing = async ({ market }: ILandingProps) => {
  const welcomePageConfig = await getWelcomePageConfig(market)

  return (
    <>
      <Banner market={market} />
      <div className={styles.content}>
        <LandingGamesSection />
        <LandingGamesSection />
      </div>
    </>
  )
}
