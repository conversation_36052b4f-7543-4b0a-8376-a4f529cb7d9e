'use client'
import React, { useState } from 'react'
import type { AxiosError } from 'axios'
import { clsx } from 'clsx'
import { useRouter } from 'next/navigation'
import type { SubmitHandler } from 'react-hook-form'
import { Controller, useForm } from 'react-hook-form'
import type { ILoginSchema } from '@/modules/auth/auth.schema'
import { loginSchema } from '@/modules/auth/auth.schema'
import { authService } from '@/services/auth.service'
import { getAppRouter } from '@/services/router.service'
import { Alert } from '@heroui/alert'
import { Button } from '@heroui/button'
import { Form } from '@heroui/form'
import { Input } from '@heroui/input'
import { zodResolver } from '@hookform/resolvers/zod'
import styles from '@screens/login/components/LoginForm.module.scss'

export const LoginForm = () => {
  const [loginError, setLoginError] = useState('')
  const [pending, setPending] = useState(false)

  const form = useForm<ILoginSchema>({
    defaultValues: {
      email: '<EMAIL>',
      password: 'admin',
    },
    resolver: zodResolver(loginSchema),
  })

  const router = useRouter()

  const handleSubmit: SubmitHandler<ILoginSchema> = async data => {
    setPending(true)
    setLoginError('')

    try {
      const response = await authService.login(data)

      if (!response?.error) {
        router.replace(getAppRouter().casino)
      } else {
        throw new Error(response?.code || response?.error || 'Login failed')
      }
    } catch (error: unknown | AxiosError) {
      const err = error as AxiosError
      console.error('Login error:', { ...err }, err.message)
      setLoginError(err.message || err.code || 'An error occurred during login')
    } finally {
      setPending(false)
    }
  }

  return (
    <>
      <Form onSubmit={form.handleSubmit(handleSubmit)} className={clsx(styles.form)} validationBehavior="aria">
        <div className="flex w-full flex-wrap gap-4">
          <Controller
            name="email"
            control={form.control}
            render={({ field }) => {
              return (
                <Input
                  label="Email"
                  type="email"
                  autoComplete="email"
                  {...field}
                  {...form.register('email')}
                  errorMessage={form.formState.errors['email']?.message}
                  isInvalid={!!form.formState.errors['email']?.message}
                />
              )
            }}
          />
          <Controller
            name="password"
            control={form.control}
            render={({ field }) => (
              <Input
                label="Password"
                type="password"
                autoComplete="current-password"
                {...field}
                {...form.register('password')}
                errorMessage={form.formState.errors['password']?.message}
                isInvalid={!!form.formState.errors['password']?.message}
              />
            )}
          />
          {!!loginError && (
            <div className="flex flex-col gap-4 w-full">
              <Alert color="danger" variant="solid" description={loginError} title={'Login Error'} />
            </div>
          )}
          <Button color="secondary" type="submit" isLoading={pending}>
            Login
          </Button>
        </div>
      </Form>
    </>
  )
}
