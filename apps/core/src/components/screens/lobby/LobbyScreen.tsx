import React from 'react'
import { getCasinoPageLayoutConfig, getLiveCasinoPageLayoutConfig } from '@/network/server-utils/s3/getters'
import { DynamicPageRenderer } from '@components/DynamicPageRenderer'
import { LobbyType } from '@repo/constants/lobby'
import type { RouteParams } from '@repo/types/nextjs'
import styles from '@screens/lobby/Lobby.module.scss'

type LobbyScreenProps = RouteParams & {
  type: LobbyType
}

export const LobbyScreen = async ({ market, type }: LobbyScreenProps) => {
  let lobbyPageConfig
  if (type === LobbyType.Casino) {
    lobbyPageConfig = await getCasinoPageLayoutConfig()
  } else if (type === LobbyType.LiveCasino) {
    lobbyPageConfig = await getLiveCasinoPageLayoutConfig()
  } else {
    throw new Error(`Unsupported lobby type: ${type}`)
  }

  if (!lobbyPageConfig) {
    return (
      <div className={styles.container}>
        <div>Failed to load lobby page configuration</div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <DynamicPageRenderer config={lobbyPageConfig} market={market} />
    </div>
  )
}
