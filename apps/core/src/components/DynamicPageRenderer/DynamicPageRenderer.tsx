import React from 'react'
import type { FC } from 'react'
import { getComponent } from '@components/DynamicPageRenderer/utils/componentRegistry'
import { isValidPageConfig, isValidSection } from '@components/DynamicPageRenderer/utils/pageConfigUtils'
import type { GetCasinoPageLayoutConfigGlobalResponse } from '@repo/types/api/s3/casino-page-config'
import styles from '@components/DynamicPageRenderer/DynamicPageRenderer.module.scss'

interface IDynamicPageRendererProps {
  config: GetCasinoPageLayoutConfigGlobalResponse
  market: string
}

const DynamicPageRenderer: FC<IDynamicPageRendererProps> = ({ config, market }) => {
  if (!isValidPageConfig(config)) {
    console.error('Invalid page configuration:', config)
    return (
      <div className={styles.error}>
        <p>Invalid page configuration</p>
      </div>
    )
  }

  if (!config.sections || config.sections.length === 0) {
    return (
      <div className={styles.error}>
        <p>No sections configured</p>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      {config.sections.map((section, index) => {
        if (!isValidSection(section)) {
          console.warn(`Invalid section at index ${index}:`, section)
          return (
            <div key={index} className={styles.missingComponent}>
              <p>Invalid section configuration at position {index + 1}</p>
            </div>
          )
        }

        const componentName = section.component

        const Component = getComponent(componentName)

        if (!Component) {
          return (
            <div key={index} className={styles.missingComponent}>
              <p>Component &quot;{componentName}&quot; not found</p>
              <p>Type: {section.type}</p>
              <p>Position: {index + 1}</p>
            </div>
          )
        }

        return (
          <div key={index} className={styles.section} data-component={componentName} data-type={section.type}>
            <Component market={market} />
          </div>
        )
      })}
    </div>
  )
}

export default DynamicPageRenderer
