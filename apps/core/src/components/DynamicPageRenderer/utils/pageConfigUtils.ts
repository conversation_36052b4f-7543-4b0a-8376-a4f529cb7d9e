import type { GetCasinoPageLayoutConfigGlobalResponse } from '@repo/types/api/s3/casino-page-config'

// Section types
export type SectionType = 'widget' | 'page'

export interface ISectionConfig {
  type: SectionType
  component: string
}

export function isValidSection(section: unknown): section is ISectionConfig {
  return (
    typeof section === 'object' &&
    section !== null &&
    'type' in section &&
    'component' in section &&
    typeof (section as ISectionConfig).type === 'string' &&
    typeof (section as ISectionConfig).component === 'string' &&
    ['widget', 'page'].includes((section as ISectionConfig).type)
  )
}

export function isValidPageConfig(config: unknown): config is GetCasinoPageLayoutConfigGlobalResponse {
  return (
    typeof config === 'object' &&
    config !== null &&
    'sections' in config &&
    Array.isArray((config as GetCasinoPageLayoutConfigGlobalResponse).sections) &&
    (config as GetCasinoPageLayoutConfigGlobalResponse).sections.every(isValidSection)
  )
}

export function getSectionsByType(
  config: GetCasinoPageLayoutConfigGlobalResponse,
  type: SectionType,
): ISectionConfig[] {
  return config.sections.filter(
    (section): section is ISectionConfig => isValidSection(section) && section.type === type,
  )
}

export function getWidgetSections(config: GetCasinoPageLayoutConfigGlobalResponse): ISectionConfig[] {
  return getSectionsByType(config, 'widget')
}

export function getPageSections(config: GetCasinoPageLayoutConfigGlobalResponse): ISectionConfig[] {
  return getSectionsByType(config, 'page')
}
