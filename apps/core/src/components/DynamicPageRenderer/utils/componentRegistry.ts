import type { IDynamicComponentProps } from '@/types/components'
import { Slots } from '@modules/slots/Slots'
import { PromotionsWidget } from '@modules/widgets/PromotionsWidget/PromotionsWidget'
import { QuickDepositWidget } from '@modules/widgets/QuickDepositWidget/QuickDepositWidget'
import { WinnersWidget } from '@modules/widgets/WinnersWidget/WinnersWidget'

// Re-export the interface for backward compatibility
export type { IDynamicComponentProps } from '@/types/components'

export const widgetComponents = {
  WinnersWidget,
  PromotionsWidget,
  QuickDepositWidget,
} as const

export const pageComponents = {
  Slots,
} as const

export const componentRegistry = {
  ...widgetComponents,
  ...pageComponents,
} as const

export type WidgetComponentName = keyof typeof widgetComponents
export type PageComponentName = keyof typeof pageComponents
export type ComponentName = keyof typeof componentRegistry

export function getComponent(componentName: string): React.ComponentType<IDynamicComponentProps> | undefined {
  return componentRegistry[componentName as ComponentName] as React.ComponentType<IDynamicComponentProps> | undefined
}
