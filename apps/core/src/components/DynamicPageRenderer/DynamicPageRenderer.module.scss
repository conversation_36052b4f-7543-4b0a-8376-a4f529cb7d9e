@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(20px);
}

.section {
  width: 100%;
}

.error {
  padding: calculate-rem(20px);
  background-color: $color-error-container;
  color: $color-error;
  border-radius: calculate-rem(8px);
  text-align: center;
}

.missingComponent {
  padding: calculate-rem(16px);
  background-color: $color-surface-200;
  color: $color-surface-900;
  border-radius: calculate-rem(8px);
  border: 1px dashed $color-surface-400;
  margin: calculate-rem(8px) 0;

  p {
    margin: calculate-rem(4px) 0;
    font-size: calculate-rem(14px);
  }
}
