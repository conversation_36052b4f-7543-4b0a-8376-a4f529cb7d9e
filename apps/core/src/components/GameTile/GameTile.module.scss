@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.gameTile {
  position: relative;
  width: $game-tile-default-width;
  height: $game-tile-default-height;
  overflow: hidden;
  flex-shrink: 0;
  border-radius: $radius-md;
}

.gameTile img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gameName {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: $color-scrim;
  color: $color-surface-1000;
  text-align: center;
  padding: calculate-rem(5px) 0;
}
