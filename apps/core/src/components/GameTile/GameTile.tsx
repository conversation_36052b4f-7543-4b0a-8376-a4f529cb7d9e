import React from 'react'
import { GAME_TILE_DEFAULT_HEIGHT, GAME_TILE_DEFAULT_WIDTH } from '@components/GameTile/GameTile.settings'
import { ImgIx } from '@components/ImgIx'
import styles from '@components/GameTile/GameTile.module.scss'

type GameTileProps = {
  game: {
    name: string
    meta: {
      thumbnail: {
        src: string
      }
    }
  }
  priority?: boolean
}

const GameTile: React.FC<GameTileProps> = ({ game, priority = false }) => {
  return (
    <div className={styles.gameTile}>
      <ImgIx
        src={game.meta.thumbnail.src}
        alt={game.name}
        width={GAME_TILE_DEFAULT_WIDTH}
        height={GAME_TILE_DEFAULT_HEIGHT}
        priority={priority}
        loading={priority ? 'eager' : 'lazy'}
        unoptimized
      />
      {/* <ImgIxWithResize
        src={game.meta.thumbnail.src}
        alt={game.name}
        width={GAME_TILE_DEFAULT_WIDTH}
        height={GAME_TILE_DEFAULT_HEIGHT}
      /> */}
      <div className={styles.gameName}>{game.name}</div>
    </div>
  )
}

export default GameTile
