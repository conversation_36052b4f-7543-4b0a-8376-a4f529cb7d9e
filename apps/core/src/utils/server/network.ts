import 'server-only'
import { headers } from 'next/headers'
import type { NextRequest } from 'next/server'
import type { Session } from 'next-auth'
import { auth } from '@/auth'
import { supportedMarketRegex } from '@/middlewares/utils'

export async function getClientIp(req?: any) {
  const forwarded = (await headers()).get('x-forwarded-for')
  return forwarded ? forwarded.split(',')[0] : req?.connection?.remoteAddress
}

export async function detectMarketByIp() {
  // TODO to be implemented based on the ipdata or CF header
  const ip = await getClientIp()
  return 'fi'
}

function detectMarketByUserInfo(sessionInfo: Session) {
  // TODO to be implemented based on the user country map or whatever
  const userInfo = sessionInfo.user
  const MAP = {
    SK: 'mk',
    MT: 'mt',
  }
  return MAP[userInfo?.geo?.countryCode as 'SK'] || 'en'
}

export function extractMarketFromUrl(url: string): string | null {
  const match = url.match(supportedMarketRegex)
  return match && match[1] ? match[1] : null
}

export async function detectMarketByRequest(req?: NextRequest) {
  const sessionInfo = await auth()
  const pathname = req?.nextUrl?.pathname || ''

  const isAuthenticated = !!sessionInfo?.accessToken && !!sessionInfo?.user
  if (isAuthenticated) {
    return detectMarketByUserInfo(sessionInfo)
  }
  return extractMarketFromUrl(pathname) || detectMarketByIp()
}
