// This file was auto-generated by generate-heroui-config.mjs
// Do not edit directly - run "yarn generate-heroui-config" to regenerate

export default {
  themes: {
    light: {
      colors: {
        primary: {
          50: '#ffffff',
          100: '#fbfbfb',
          200: '#e2e2e2',
          300: '#c8c8c8',
          400: '#afafaf',
          500: '#a2a2a2',
          600: '#959595',
          700: '#7c7c7c',
          800: '#626262',
          900: '#494949',
          DEFAULT: '#a2a2a2',
          foreground: '#f0f0f0',
        },
        secondary: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#ffffff',
          300: '#ffffff',
          400: '#fdfdfd',
          500: '#f0f0f0',
          600: '#e3e3e3',
          700: '#cacaca',
          800: '#b0b0b0',
          900: '#979797',
          DEFAULT: '#f0f0f0',
          foreground: '#222222',
        },
        success: {
          50: '#fbfbfb',
          100: '#e1e1e1',
          200: '#c8c8c8',
          300: '#aeaeae',
          400: '#959595',
          500: '#888888',
          600: '#7b7b7b',
          700: '#626262',
          800: '#484848',
          900: '#2f2f2f',
          DEFAULT: '#888888',
          foreground: '#222222',
        },
        danger: {
          50: '#fbfbfb',
          100: '#e1e1e1',
          200: '#c8c8c8',
          300: '#aeaeae',
          400: '#959595',
          500: '#888888',
          600: '#7b7b7b',
          700: '#626262',
          800: '#484848',
          900: '#2f2f2f',
          DEFAULT: '#888888',
          foreground: '#000000',
        },
        warning: {
          50: '#fbfbfb',
          100: '#e1e1e1',
          200: '#c8c8c8',
          300: '#aeaeae',
          400: '#959595',
          500: '#888888',
          600: '#7b7b7b',
          700: '#626262',
          800: '#484848',
          900: '#2f2f2f',
          DEFAULT: '#888888',
          foreground: '#000000',
        },
        background: '#f8f8f8',
        foreground: '#606060',
        focus: '#333333',
        overlay: '#606060',
        default: {
          50: '#969696',
          100: '#7c7c7c',
          200: '#636363',
          300: '#494949',
          400: '#303030',
          500: '#232323',
          600: '#161616',
          700: '#000000',
          800: '#000000',
          900: '#000000',
          DEFAULT: '#232323',
          foreground: '#f0f0f0',
        },
        content1: {
          DEFAULT: '#a2a2a2',
          foreground: '#f0f0f0',
        },
        content2: {
          DEFAULT: '#f0f0f0',
          foreground: '#222222',
        },
        content3: {
          DEFAULT: '#e0e0e0',
          foreground: '#ffffff',
        },
        content4: {
          DEFAULT: '#e0e0e0',
          foreground: '#ffffff',
        },
      },
    },
    dark: {
      colors: {
        primary: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#f0f0f0',
          300: '#d6d6d6',
          400: '#bdbdbd',
          500: '#b0b0b0',
          600: '#a3a3a3',
          700: '#8a8a8a',
          800: '#707070',
          900: '#575757',
          DEFAULT: '#b0b0b0',
          foreground: '#222222',
        },
        secondary: {
          50: '#9f9f9f',
          100: '#858585',
          200: '#6c6c6c',
          300: '#525252',
          400: '#393939',
          500: '#2c2c2c',
          600: '#1f1f1f',
          700: '#060606',
          800: '#000000',
          900: '#000000',
          DEFAULT: '#2c2c2c',
          foreground: '#f0f0f0',
        },
        success: {
          50: '#fbfbfb',
          100: '#e1e1e1',
          200: '#c8c8c8',
          300: '#aeaeae',
          400: '#959595',
          500: '#888888',
          600: '#7b7b7b',
          700: '#626262',
          800: '#484848',
          900: '#2f2f2f',
          DEFAULT: '#888888',
          foreground: '#f0f0f0',
        },
        danger: {
          50: '#fbfbfb',
          100: '#e1e1e1',
          200: '#c8c8c8',
          300: '#aeaeae',
          400: '#959595',
          500: '#888888',
          600: '#7b7b7b',
          700: '#626262',
          800: '#484848',
          900: '#2f2f2f',
          DEFAULT: '#888888',
          foreground: '#000000',
        },
        warning: {
          50: '#fbfbfb',
          100: '#e1e1e1',
          200: '#c8c8c8',
          300: '#aeaeae',
          400: '#959595',
          500: '#888888',
          600: '#7b7b7b',
          700: '#626262',
          800: '#484848',
          900: '#2f2f2f',
          DEFAULT: '#888888',
          foreground: '#000000',
        },
        background: '#181818',
        foreground: '#4a4a4a',
        focus: '#a0a0a0',
        overlay: '#4a4a4a',
        default: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#ffffff',
          300: '#ffffff',
          400: '#fdfdfd',
          500: '#f0f0f0',
          600: '#e3e3e3',
          700: '#cacaca',
          800: '#b0b0b0',
          900: '#979797',
          DEFAULT: '#f0f0f0',
          foreground: '#232323',
        },
        content1: {
          DEFAULT: '#b0b0b0',
          foreground: '#222222',
        },
        content2: {
          DEFAULT: '#2c2c2c',
          foreground: '#f0f0f0',
        },
        content3: {
          DEFAULT: '#444444',
          foreground: '#f0f0f0',
        },
        content4: {
          DEFAULT: '#444444',
          foreground: '#f0f0f0',
        },
      },
    },
  },
  layout: {
    radius: {
      small: '0.5rem',
      medium: '0.75rem',
      large: '1rem',
    },
    borderWidth: {
      small: '1px',
      medium: '1px',
      large: '1px',
    },
  },
}
