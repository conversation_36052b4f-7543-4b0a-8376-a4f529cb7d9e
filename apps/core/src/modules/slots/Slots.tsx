import type { <PERSON> } from 'react'
import React from 'react'
import { getCasinoConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicComponentProps } from '@/types/components'
import { GamesSection } from '@components/GamesSection'
import styles from '@modules/slots/Slots.module.scss'

export const Slots: FC<IDynamicComponentProps> = async ({ market }) => {
  const casinoConfig = await getCasinoConfig(market)

  return (
    <div className={styles.container}>
      <h2>Slots</h2>
      {casinoConfig?.sections?.map(section => (
        <GamesSection key={section.id} section={section} market={market} />
      ))}
    </div>
  )
}
