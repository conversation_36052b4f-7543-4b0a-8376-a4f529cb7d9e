import type { Session } from 'next-auth'
import type { SessionContextValue } from 'next-auth/react'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { Keyable } from '@repo/types/common'

export interface IAuthState {
  accessToken: string | null
  refreshToken: string | null
  userInfo: Keyable | null
  setAccessToken: (token: string | null) => void
  setUserInfo: (data: Keyable | null) => void
  getIsAuthenticated: () => boolean
  status: SessionContextValue['status']
}

export const createAuthStore = (initState?: (Session & { status?: IAuthState['status'] }) | null) =>
  create(
    devtools<IAuthState>((set, get) => ({
      accessToken: initState?.accessToken || null,
      refreshToken: initState?.refreshToken || null,
      userInfo: initState?.user || null,
      setAccessToken: token => set({ accessToken: token }),
      setUserInfo: data => set({ userInfo: data }),
      clear: () => set({ accessToken: null, userInfo: null }),
      getIsAuthenticated: () => !!get().accessToken,
      status: (() => {
        if (initState?.accessToken) return 'authenticated'
        if (initState === null) return 'unauthenticated'
        return 'loading'
      })(),
    })),
  )
