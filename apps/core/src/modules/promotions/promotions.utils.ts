import {
  RH<PERSON><PERSON>AYER_DISPLAY_TAG_KEY,
  R<PERSON><PERSON><PERSON><PERSON>YER_DISPLAY_TAG_VALUES,
  RHINOLAYER_TARGET_KEY,
  RHINOLAYER_TARGET_VALUES,
} from '@repo/constants/rhinoLayer'
import type { IRlResponse } from '@repo/types/api/rl/index'
import type { IRlPromotion, IRlPromotionsResponse } from '@repo/types/api/rl/promotions'
import type { Keyable } from '@repo/types/common'

type GetPromotionsTransformResponseProps = {
  isLoggedIn: boolean
}

export const getPromotionsTransformResponse = (
  response: IRlResponse<IRlPromotionsResponse>,
  { isLoggedIn }: GetPromotionsTransformResponseProps,
) => {
  const data = response.data
  data.currentItemsCount = data?.payload?.length || 0
  if (!data.payload) return data || null

  data.payload = data.payload?.filter(promo => {
    if (
      promo.tags.some(
        (t: IRlPromotion['tags'][0]) =>
          t.name === `${RHINOLAYER_DISPLAY_TAG_KEY}:${RHINOLAYER_DISPLAY_TAG_VALUES.APP_ONLY}`,
      )
    ) {
      return
    }

    // filter out excess welcome offers, we can only have one, and affiliated offers take precedence
    //   const welcomeOffer = getWelcomeOffer(promotions, args?.affiliateId)
    //   if (promo.isWelcomeOffer && promo.id !== welcomeOffer?.id) {
    //     return
    //   }

    //   // filter out promo ctas
    promo.ctas = filterCtasByTagNames(promo.ctas, [
      `${RHINOLAYER_DISPLAY_TAG_KEY}:${RHINOLAYER_DISPLAY_TAG_VALUES.WEB_ONLY}`,
      `${RHINOLAYER_TARGET_KEY}:${
        isLoggedIn ? RHINOLAYER_TARGET_VALUES.LOGGED_OUT : RHINOLAYER_TARGET_VALUES.LOGGED_IN
      }`,
    ])

    return promo
  })

  return data
}

const filterCtasByTagNames = (ctas: IRlPromotion['ctas'], tagNames: string[]) =>
  Object.fromEntries(
    Object.entries(ctas)?.filter(
      ([key, value]) => !value.body?.tags?.some((tag: Keyable) => tagNames.includes(tag.name)),
    ),
  )

const getWelcomeOffer = (promotions: IRlPromotion[], affiliateId: string | number | null | undefined) => {
  const affiliateWelcomeOffer = affiliateId
    ? promotions.find(p => p.isWelcomeOffer && p.affiliateIds?.includes(affiliateId?.toString()))
    : undefined
  const defaultWelcomeOffer = affiliateWelcomeOffer ? undefined : promotions.find(p => p.isWelcomeOffer)
  return affiliateWelcomeOffer || defaultWelcomeOffer
}
