import { getSession, signIn, signOut } from 'next-auth/react'
import { refreshTokenAction } from '@/actions/refreshToken'
import { auth } from '@/auth'
import { getAuthStore } from '@/modules/auth/auth.provider'
import type { ILoginSchema } from '@/modules/auth/auth.schema'
import { loginSchema } from '@/modules/auth/auth.schema'

export type LoginErrorType =
  | {
      email: string
      password: string
    }
  | string

class AuthService {
  constructor() {
    // Initialization code here
  }

  async login(formData: ILoginSchema) {
    'use client'
    const { data, error: zodError } = loginSchema.safeParse(formData)
    if (zodError) {
      throw zodError
    }
    console.log('try login with', data)
    return await signIn('credentials', {
      ...data,
      redirect: false,
    })
  }

  async logout() {
    'use client'
    await signOut({ redirectTo: '/' })
  }

  async refreshToken(refreshToken: string) {
    'use client'
    if (!refreshToken) {
      console.warn('No refresh token found')
      return null
    }
    const newTokens = await refreshTokenAction(refreshToken)
    await getSession()
    return newTokens
  }

  async isAuthenticated(): Promise<boolean> {
    if (typeof window !== 'undefined') {
      return !!getAuthStore()?.getIsAuthenticated()
    }
    const sessionInfo = await auth()
    if (sessionInfo?.accessToken) {
      return true
    }
    return false
  }
}

export const authService = new AuthService()
