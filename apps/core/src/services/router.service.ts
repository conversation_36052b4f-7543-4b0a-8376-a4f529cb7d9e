class RouterService {
  constructor(private market?: string) {}

  get marketPrefix(): string {
    return this.market ? `/${this.market}` : ''
  }

  get home(): string {
    return `${this.marketPrefix || '/'}`
  }

  get login(): string {
    return `${this.marketPrefix}/login`
  }

  get register(): string {
    return `${this.marketPrefix}/register`
  }

  get dashboard(): string {
    return `${this.marketPrefix}/dashboard`
  }

  get casino(): string {
    return `${this.marketPrefix}/casino`
  }

  get liveCasino(): string {
    return `${this.marketPrefix}/live-casino`
  }
}

export const getAppRouter = (market?: string) => new RouterService(market)
export const useAppRouter = (market?: string) => new RouterService(market)
