import axios, { AxiosError } from 'axios'
import type {
  AxiosResponse,
  AxiosRequestConfig,
  CancelToken,
  AxiosInstance,
  InternalAxiosRequestConfig,
  AxiosHeaders,
} from 'axios'
import { signOut } from 'next-auth/react'
import { v4 } from 'uuid'
import { getAccessToken, getAuthStore } from '@/modules/auth/auth.provider'
import { withRetryInterceptor, withRefreshTokenInterceptor } from '@/network/interceptors'
import { addToast } from '@heroui/toast'
import { type BaseQueryFn, type FetchBaseQueryError } from '@reduxjs/toolkit/query'
import { SESSION_EXPIRED_ERROR_CODE } from '@repo/constants/errors'
import { BACKEND_AUTHENTICATION_KEY } from '@repo/constants/network'
import { isUrlAbsolute } from '@repo/helpers/urlHelpers'
import type { Keyable } from '@repo/types/common'

interface IAxiosBaseQueryParams {
  baseUrl: string | (() => Promise<string>)
  prepareHeaders?: (headers: Map<string, string>) => Keyable
  responseInterceptor?: (resp: AxiosResponse<any, any>) => AxiosResponse<any, any>
  requestInterceptor?: (request: InternalAxiosRequestConfig<any>) => InternalAxiosRequestConfig<any>
  instance?: AxiosInstance
  skipToastForErrorCodes?: number[]
}

const MAINTENANCE_MODE_ERROR_CODES = [599, 502, 503]
const EXCLUDED_FROM_LOGGER_ERROR_CODES = [599, 401]
const EXCLUDED_FROM_TOAST_ERROR_CODES = [599, 403]
const EXCLUDED_FROM_LOGGER_ROUTES: string[] = []

export type AxiosBaseQueryFn = BaseQueryFn<
  {
    url: string
    method?: AxiosRequestConfig['method']
    body?: AxiosRequestConfig['data']
    params?: AxiosRequestConfig['params']
    headers?: AxiosRequestConfig['headers']
    onUploadProgress?: AxiosRequestConfig['onUploadProgress']
    cancelToken?: CancelToken
    showToastOnNetworkError?: boolean
    toastNetworkErrorMessage?: string
    skipLogger?: boolean | ((request: AxiosError) => boolean)
    refreshTokenOn401?: boolean
    bypassIsConfigReadyCheck?: boolean
    responseInterceptor?: IAxiosBaseQueryParams['responseInterceptor'] | false
  },
  unknown,
  FetchBaseQueryError | undefined
>

export const axiosBaseQuery = ({
  baseUrl: _baseUrl,
  prepareHeaders,
  responseInterceptor,
  requestInterceptor,
  skipToastForErrorCodes = [],
}: IAxiosBaseQueryParams): AxiosBaseQueryFn => {
  const axiosInstance = axios.create({ timeout: 30000 })

  if (requestInterceptor) {
    axiosInstance.interceptors.request.use(requestInterceptor)
  }

  withRetryInterceptor(axiosInstance)

  return async ({
    url,
    method = 'GET',
    body,
    params,
    headers: queryHeaders,
    showToastOnNetworkError = true,
    toastNetworkErrorMessage,
    skipLogger = false,
    refreshTokenOn401 = true,
    onUploadProgress,
    cancelToken,
    responseInterceptor: queryResponseInterceptor,
  }) => {
    try {
      let baseUrl = _baseUrl as string
      if (typeof _baseUrl === 'function') {
        baseUrl = await _baseUrl()
      }
      if (!isUrlAbsolute(baseUrl)) {
        throw new AxiosError(`baseUrl "${baseUrl}" is invalid, request prevented`, undefined, {
          url,
          headers: queryHeaders as AxiosHeaders,
        })
      }
      const headers = new Map()

      const accessToken = getAccessToken()
      if (accessToken) {
        headers.set(BACKEND_AUTHENTICATION_KEY, 'Bearer ' + accessToken)
      }

      if (prepareHeaders) {
        const extraHeaders = prepareHeaders(headers)
        Object.keys(extraHeaders || {}).forEach(key => {
          headers.set(key, extraHeaders[key])
        })
      }

      if (queryHeaders) {
        Object.keys(queryHeaders).forEach(key => {
          headers.set(key, queryHeaders[key] as string)
        })
      }

      headers.set('X-Correlation-Id', headers.get('X-Correlation-Id') || `${v4()}_${Date.now()}`)

      const reqConfig: AxiosRequestConfig<any> = {
        baseURL: baseUrl,
        url: url,
        method,
        data: body,
        params,
        onUploadProgress,
        cancelToken,
        headers: Object.fromEntries(headers),
        withCredentials: false,
      }

      let result
      if (refreshTokenOn401) {
        result = await withRefreshTokenInterceptor(axiosInstance.request(reqConfig), axiosInstance)
      } else {
        result = await axiosInstance.request(reqConfig)
      }

      if (result.status >= 400) {
        throw new AxiosError(undefined, result.statusText, result.config, result.request, result)
      }
      const _responseInterceptor = queryResponseInterceptor ?? responseInterceptor
      if (_responseInterceptor) {
        result = _responseInterceptor(result)
      }
      const { data, ...meta } = result
      return { data, meta }
    } catch (axiosError) {
      const err = axiosError as AxiosError
      const responseStatus = err.response?.status || 0
      const excludedFromToastErrorCodes = EXCLUDED_FROM_TOAST_ERROR_CODES.concat(skipToastForErrorCodes)
      console.error('baseQuery error', err?.config?.url, err?.response?.status, err?.message, err?.response?.data)
      if (showToastOnNetworkError && responseStatus && !excludedFromToastErrorCodes.includes(responseStatus)) {
        const toastMessage = toastNetworkErrorMessage || err.message
        addToast({
          title: 'Network Error',
          description: toastMessage,
          color: 'danger',
        })
      }

      if (
        (responseStatus === 401 &&
          !err.config?.url?.includes('/logout') &&
          !err.config?.url?.includes('/auth/mfa/complete')) || // To prevent logout if the user input wrong authCode
        responseStatus === SESSION_EXPIRED_ERROR_CODE
      ) {
        console.log('auth needed', err.config?.url)
        const authStore = getAuthStore()
        if (authStore?.status === 'authenticated' || !!authStore?.accessToken) {
          console.info('Request failed with status code 401, user will be logged out', { error: err })
          await signOut()
        }
      }
      if (MAINTENANCE_MODE_ERROR_CODES.includes(responseStatus)) {
        //  getStore().dispatch(setRlMaintenanceModeEnabled(true))
      }

      const _skipLogger = typeof skipLogger === 'function' ? skipLogger(err) : skipLogger

      if (responseStatus >= 400 && !EXCLUDED_FROM_LOGGER_ERROR_CODES.includes(responseStatus) && !_skipLogger) {
        const skipLogError = EXCLUDED_FROM_LOGGER_ROUTES.some(route => {
          if (err.config?.url?.includes(route)) {
            return true
          }
          return false
        })

        if (!skipLogError) {
          console.error(err, {
            tags: { status: err.response?.status, statusText: err.response?.statusText },
            cause: err,
          })
        }
      }

      return {
        error: {
          status: responseStatus,
          data: err.response?.data || err.message,
        },
      }
    }
  }
}
