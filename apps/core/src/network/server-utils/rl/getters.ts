import { rlFetchApi } from '@/network/server-utils/rl/RLFetchApi'
import { getLicense } from '@repo/helpers/locale'
import type { IGetPromotionsProps } from '@repo/types/api/rl/promotions'
import 'server-only'

export const getBanners = async (market: string) => {
  const banners = await rlFetchApi.getBanners({ license: getLicense(market), market }) // TODO dynamic parameters
  return banners?.payload
}

export const getPromotions = async (props: IGetPromotionsProps) => {
  const response = await rlFetchApi.getPromotions(props)
  return response
}
