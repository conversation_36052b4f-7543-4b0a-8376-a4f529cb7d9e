import 'server-only'
import { envVars } from '@/env'
import { getPromotionsTransformResponse } from '@/modules/promotions/promotions.utils'
import { BaseServerFetchApi } from '@/network/server-utils/BaseServerFetchApi'
import { rlEndpoints } from '@repo/api/endpoints/rl'
import { extractLocale } from '@repo/helpers/locale'
import type { IGetBannersProps, IGetBannersResponse } from '@repo/types/api/rl/banners'
import type { IRlResponse } from '@repo/types/api/rl/index'
import type { IGetPromotionsProps, IRlPromotionsResponse } from '@repo/types/api/rl/promotions'

class RLFetch<PERSON><PERSON> extends BaseServerFetchApi {
  baseUrl = envVars.NEXT_PUBLIC_BACKEND_URL
  revalidate = 5
  defaultHeaders: HeadersInit = {
    Accept: 'application/json;v=1',
    'Content-Type': 'application/json',
  }

  async getBanners(params: IGetBannersProps) {
    return this.fetch<IGetBannersResponse>(rlEndpoints.banners, {
      params,
    }).then(response => response?.data)
  }

  async getPromotions({ isLoggedIn, ...params }: IGetPromotionsProps) {
    const [iso2, rlMarket] = extractLocale(params.market)
    return this.fetch<IRlResponse<IRlPromotionsResponse>>(rlEndpoints.promotions, {
      params: {
        ...params,
        iso2,
        market: rlMarket,
      },
    }).then(response => (response ? getPromotionsTransformResponse(response, { isLoggedIn }) : null))
  }
}

export const rlFetchApi = new RLFetchApi()
