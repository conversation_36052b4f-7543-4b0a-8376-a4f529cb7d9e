import 'server-only'
import type { Keyable } from '@repo/types/common'

export class BaseServerFetchApi {
  baseUrl = 'unknown'
  revalidate = 15
  defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  }

  async fetch<T>(endpoint: string, options: RequestInit & { params?: Keyable } = {}): Promise<T | null> {
    try {
      const { params, ...fetchOptions } = options

      let url = `${this.baseUrl}${endpoint}`
      if (params && Object.keys(params).length > 0) {
        const queryParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value))
          }
        })

        const queryString = queryParams.toString()
        if (queryString) {
          url += `?${queryString}`
        }
      }

      const response = await fetch(url, {
        method: 'GET',
        ...fetchOptions,
        headers: { ...this.defaultHeaders, ...fetchOptions.headers },
        next: {
          revalidate: this.revalidate,
          ...fetchOptions.next,
        },
      })

      if (!response.ok) {
        try {
          console.error(await response.json())
        } catch (error) {
          console.error(`Failed to parse error response from ${url}`, error)
        }
        throw new Error(`[HTTP error] status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`Error fetching ${endpoint}:`, error)
      return null
    }
  }
}
