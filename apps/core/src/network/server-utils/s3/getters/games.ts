import 'server-only'
import { auth } from '@/auth'
import { gamesService } from '@/services/Games.service'
import { detectMarketByIp } from '@/utils/server/network'
import type { IRlGame } from '@repo/types/games'

export const getAvailableGames = async (market: string) => {
  console.log('getAvailableGames for market:', market)
  if (!market) {
    console.warn('Market is not provided, returning empty games list')
    return [] as IRlGame[]
  }

  const allGames = await gamesService.getAllGames()
  if (!Array.isArray(allGames)) {
    return [] as IRlGame[]
  }

  const countryByIp = await detectMarketByIp()
  const session = await auth()

  return gamesService.prepareGames(allGames, {
    countryByIp,
    regionOfAction: countryByIp,
    countryOfRegistration: session?.user?.geo?.countryCode,
    license: session?.user.userJurisdiction || 'curacao',
  })
}

export const getAvailableGamesMap = async (market: string) => {
  console.log('getAvailableGamesMap for market:', market)
  if (!market) {
    console.warn('Market is not provided, returning empty games list')
    return {} as ReturnType<typeof gamesService.prepareGamesMap>
  }

  const allGames = await gamesService.getAllGames()
  if (!Array.isArray(allGames)) {
    return {} as ReturnType<typeof gamesService.prepareGamesMap>
  }

  const countryByIp = await detectMarketByIp()
  const session = await auth()

  return gamesService.prepareGamesMap(allGames, {
    countryByIp,
    regionOfAction: countryByIp,
    countryOfRegistration: session?.user?.geo?.countryCode,
    license: session?.user.userJurisdiction || 'curacao',
  })
}
