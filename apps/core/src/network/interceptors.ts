import type { AxiosError, AxiosInstance, AxiosResponse } from 'axios'
import type { Optional } from 'utility-types'
import { getRefreshToken } from '@/modules/auth/auth.provider'
import { calculateExponentialDelay } from '@/network/helpers'
import { authService } from '@/services/auth.service'
import { BACKEND_AUTHENTICATION_KEY } from '@repo/constants/network'

interface IRetryConfig {
  retry: number
  retryDelay: number
  maxRetryDelay: number
  retryDelayMethod: 'static' | 'exponential'
  httpMethodsToRetry: string[]
  statusCodesToRetry: [number, number][]
}

const defaultRetryConfig: IRetryConfig = {
  retry: process.env.NODE_ENV === 'test' ? 0 : 1, // it requires to call run timers manually in tests
  retryDelay: 1000,
  maxRetryDelay: 5000,
  retryDelayMethod: 'exponential',
  httpMethodsToRetry: ['GET', 'HEAD', 'OPTIONS', 'DELETE', 'PUT', 'POST'],
  statusCodesToRetry: [
    [0, 0], //(ENOTFOUND, ETIMEDOUT, etc)
    [100, 199],
    [429, 429],
    [500, 598], // 599 is a maintenance mode on RL
  ],
}

export const withRetryInterceptor = (
  instance: AxiosInstance,
  retryConfig: Optional<IRetryConfig> = defaultRetryConfig,
) => {
  instance.interceptors.response.use(
    response => response,
    err => {
      const _retryConfig = { ...defaultRetryConfig, ...retryConfig }
      const error = err as AxiosError & { config: { retry: number; retryDelay: number } }
      const { config, response, request } = error

      if (!_retryConfig.httpMethodsToRetry?.includes(config?.method?.toUpperCase() || '') || !error.config?.url) {
        return Promise.reject(err)
      }
      const responseStatus = response?.status || 0
      const isAcceptableStatus = _retryConfig.statusCodesToRetry?.some(
        range => responseStatus >= range[0] && responseStatus <= range[1],
      )

      if (!isAcceptableStatus || !config.baseURL) {
        return Promise.reject(err)
      }
      if (config.retry === undefined) {
        config.retry = _retryConfig.retry
      }
      if (!config || !config.retry) {
        return Promise.reject(err)
      }
      const attempt = _retryConfig.retry - config.retry
      config.retry -= 1
      config.retryDelay =
        _retryConfig.retryDelayMethod === 'exponential'
          ? calculateExponentialDelay(attempt, _retryConfig.retryDelay, _retryConfig.maxRetryDelay)
          : _retryConfig.retryDelay
      const delayRetryRequest = new Promise<void>(resolve => {
        setTimeout(() => {
          console.log(
            'retry the request',
            config.baseURL,
            config.url,
            `attempt ${attempt + 1}/${_retryConfig.retry}`,
            'delay',
            config.retryDelay,
            'responseStatus',
            responseStatus,
          )
          resolve()
        }, config.retryDelay || 1000)
      })

      return delayRetryRequest.then(() => instance(config))
    },
  )
  return instance
}

export const withRefreshTokenInterceptor = (req: Promise<AxiosResponse<any, any>>, instance: AxiosInstance) =>
  req.catch(async err => {
    const error = err as AxiosError<any, any>
    if (error.response?.status === 401 && !error.config?.url?.includes('/auth/token/refresh')) {
      const refreshToken = getRefreshToken()
      if (!refreshToken) {
        throw error
      }
      console.log('Refreshing a jwt', error.config?.url)

      let retryRefreshToken = 0
      const config = error.config
      if (!retryRefreshToken && config) {
        retryRefreshToken = 1
        try {
          const response = await authService.refreshToken(refreshToken)
          console.log('refresh token response', response)
          if (!response?.success || !response?.data?.accessToken) {
            throw new Error('No tokens received: ' + response?.error)
          }

          config.headers.set(BACKEND_AUTHENTICATION_KEY, 'Bearer ' + response.data?.accessToken)
        } catch (e) {
          console.error(e)
          throw error
        }

        return instance.request(config)
      }
      throw error
    }
    throw error
  })
