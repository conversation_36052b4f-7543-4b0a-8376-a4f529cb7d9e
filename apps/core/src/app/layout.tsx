import type { Metadata } from 'next'
import dynamic from 'next/dynamic'
import { ClientProviders } from '@/client-providers'
import { envVars } from '@/env'
import { Providers } from '@/providers'
import { PrimaryFont, SecondaryFont } from '@app/layout.settings'

let Toolbar: React.ComponentType = () => null

if (process.env.NODE_ENV === 'development') {
  Toolbar = dynamic(() => import('@app/toolbar'))
}

if (process.env.USE_MSW === 'true') {
  const msw = require('@/mock/server/server').default
  msw.startMSWServer()
}

export const metadata: Metadata = {
  title: envVars.NEXT_PUBLIC_APP_NAME,
  description: 'This is a core',
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${PrimaryFont.className} ${SecondaryFont.className}`} suppressHydrationWarning>
      {/* data-scroll-locked="1" added by the sidebar adds an unnecessary right margin */}
      <body suppressHydrationWarning style={{ marginRight: '0 !important' }}>
        <Providers>
          <ClientProviders>{children}</ClientProviders>
        </Providers>
        {/* <Toolbar /> */}
      </body>
    </html>
  )
}
