import { authService } from '@/services/auth.service'
import { PageContainer } from '@modules/page/PageContainer/PageContainer'
import { PageTitle } from '@modules/page/PageTitle/PageTitle'
import { LobbyType } from '@repo/constants/lobby'
import type { PageProps } from '@repo/types/nextjs'
import { LobbyScreen } from '@screens/lobby/LobbyScreen'

export const revalidate = 10

export default async function CasinoPage({ params }: PageProps) {
  const { market } = await params
  const isAuthenticated = await authService.isAuthenticated()
  return (
    <>
      <PageContainer>
        <PageTitle title={`Casino Page - ${isAuthenticated ? 'Logged in' : 'Logged out'}`} />
        <LobbyScreen market={market} type={LobbyType.Casino} />
      </PageContainer>
    </>
  )
}
