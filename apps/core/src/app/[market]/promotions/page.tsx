import React from 'react'
import type { Metadata } from 'next'
import promotionsSettings from '@/modules/promotions/promotions.settings'
import { rlFetch<PERSON><PERSON> } from '@/network/server-utils/rl/RLFetchApi'
import { authService } from '@/services/auth.service'
import { RHINOLAYER_TARGET_KEY, RHINOLAYER_TARGET_VALUES } from '@repo/constants/rhinoLayer'
import { getLicense } from '@repo/helpers/locale'
import type { IGetPromotionsProps } from '@repo/types/api/rl/promotions'
import type { RouteParams } from '@repo/types/nextjs'
import { PromotionsScreen } from '@screens/promotions/PromotionsScreen'

type Params = Promise<RouteParams>

export const metadata: Metadata = {
  title: 'Promotions',
  description: 'Promotions page',
}

export const revalidate = 10

export default async function PromotionsPage({
  params,
}: Readonly<{
  params: Params
}>) {
  const { market } = await params
  const isLoggedIn = await authService.isAuthenticated()

  const requestParams: IGetPromotionsProps = {
    market,
    license: getLicense(market),
    tags: [
      `${RHINOLAYER_TARGET_KEY}:${RHINOLAYER_TARGET_VALUES.ALL}`,
      `${RHINOLAYER_TARGET_KEY}:${
        isLoggedIn ? RHINOLAYER_TARGET_VALUES.LOGGED_IN : RHINOLAYER_TARGET_VALUES.LOGGED_OUT
      }`,
    ],
    isLoggedIn,
    type: 0,
    limit: promotionsSettings.PER_PAGE,
    page: 1,
  }

  const promotions = await rlFetchApi.getPromotions(requestParams)
  console.log('promotions fetched:', promotions?.payload?.length, requestParams)

  return <PromotionsScreen requestParams={requestParams} initialData={promotions || undefined} />
}
