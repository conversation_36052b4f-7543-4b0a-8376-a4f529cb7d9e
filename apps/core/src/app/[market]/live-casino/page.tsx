import { authService } from '@/services/auth.service'
import { LobbyType } from '@repo/constants/lobby'
import type { PageProps } from '@repo/types/nextjs'
import { LobbyScreen } from '@screens/lobby/LobbyScreen'
import styles from '@app/[market]/casino/page.module.scss'

export const revalidate = 10

export default async function LiveCasinoPage({ params }: PageProps) {
  const { market } = await params
  const isAuthenticated = await authService.isAuthenticated()
  return (
    <div className={styles.container}>
      <h1>Live Casino Page - {isAuthenticated ? 'Logged in' : 'Logged out'}</h1>
      <LobbyScreen market={market} type={LobbyType.LiveCasino} />
    </div>
  )
}
