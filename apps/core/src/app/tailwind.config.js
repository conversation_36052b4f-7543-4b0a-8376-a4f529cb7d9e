import { heroui } from '@heroui/theme'
// eslint-disable-next-line path-alias/no-relative
import herouiConfig from '../theme/heroui.config.js'

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './../../../../apps/**/src/**/*.{ts,tsx}',
    './../../../../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}',
    './../../../../packages/ui/shadcn/*.{ts,tsx}',
    // './../../../../node_modules/@heroui/theme/dist/components/button.js',
    // './../../../../node_modules/@heroui/theme/dist/components/input.js',
    // './../../../../node_modules/@heroui/theme/dist/components/card.js',
    // './../../../../node_modules/@heroui/theme/dist/components/form.js',
    // './../../../../node_modules/@heroui/theme/dist/components/alert.js',
    // './../../../../node_modules/@heroui/theme/dist/components/spinner.js',
    // './../../../../node_modules/@heroui/theme/dist/components/toast.js',
    // './../../../../node_modules/@heroui/theme/dist/components/switch.js',
    // './../../../../node_modules/@heroui/theme/dist/components/navbar.js',
  ],
  theme: {
    extend: {},
  },
  darkMode: 'class',
  plugins: [heroui(herouiConfig)],
}
