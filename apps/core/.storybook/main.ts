import path from 'path'
import type { WebpackConfiguration } from '@storybook/core-webpack'
import type { StorybookConfig } from '@storybook/nextjs'
import baseConfig from '../../../.storybook/main'

const config: StorybookConfig = {
  ...baseConfig,
  stories: ['../src/components/**/*.stories.@(js|jsx|ts|tsx|mdx)'],
  webpackFinal: async (config: WebpackConfiguration) => {
    config.resolve = config.resolve || {}
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@components': path.resolve(__dirname, '../src/components'),
      '@modules': path.resolve(__dirname, '../src/modules'),
      '@theme': path.resolve(__dirname, '../src/theme'),
      '@app': path.resolve(__dirname, '../src/app'),
    }
    return config
  },
}

export default config
