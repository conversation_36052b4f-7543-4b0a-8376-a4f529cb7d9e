{"extends": "../../../.storybook/tsconfig.json", "compilerOptions": {"baseUrl": "../src", "paths": {"@app/*": ["./app/*"], "@screens/*": ["./components/screens/*"], "@components/*": ["./components/*"], "@modules/*": ["./modules/*"], "@theme/*": ["./theme/*"], "@/*": ["./*"]}}, "include": ["../src/**/*", "../**/*.stories.*", "../**/*.story.*", "../.storybook/*.ts", "../.storybook/*.tsx"], "exclude": ["../node_modules"]}