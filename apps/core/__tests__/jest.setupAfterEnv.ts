// setup.js
import { beforeEach, jest } from '@jest/globals'

// Create necessary mocks for Next.js components and services

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks()
})

// Mock console.warn to avoid output noise during tests
console.warn = jest.fn()

// Mock console.warn to avoid output noise during tests
// eslint-disable-next-line no-undef
global.console.warn = jest.fn()

// Add any other global setup for tests here
