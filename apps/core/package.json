{"name": "core", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "open http://localhost:3000 && NODE_OPTIONS='--inspect' npx next dev --port 3000", "dev:msw": "USE_MSW=true npx yarn dev", "build": "NODE_ENV=production npx next build", "start": "NODE_ENV=production npx next start", "lint": "npx next lint", "clear-cache": "rm -rf ./.next", "check-types": "npx tsc --noEmit", "test": "npx jest", "test:watch": "npx jest --watch", "test:coverage": "npx jest --coverage", "storybook": "storybook dev -p 6006 -c .storybook"}, "dependencies": {"@repo/api": "*", "@repo/constants": "*", "@repo/helpers": "*", "@repo/hooks": "*", "@repo/types": "*", "@repo/ui": "*"}, "devDependencies": {"dotenv": "^16.5.0"}}