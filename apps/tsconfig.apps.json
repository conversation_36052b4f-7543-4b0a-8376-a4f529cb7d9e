{
  "extends": "@repo/typescript-config/nextjs.json",
  "compilerOptions": {
    "plugins": [
      {
        "name": "next"
      },
      {
        "name": "typescript-plugin-css-modules",
        "options": {
          "classnameTransform": "camelCaseOnly"
        }
      }
    ],
    "baseUrl": "./src",
    "paths": {
      "@core/*": [
        "../../core/src/*"
      ],
      "@components/*": [
        "./components/*",
        "../../core/src/components/*"
      ],
      "@modules/*": [
        "./modules/*",
        "../../core/src/modules/*"
      ],
      "@screens/*": [
        "./components/screens/*",
        "../../core/src/components/screens/*"
      ],
      "@app/*": [
        "./app/*",
        "../../core/src/app/*"
      ],
      "@theme/*": [
        "./theme/*",
        "../../core/src/theme/*"
      ],
      "@/*": [
        "./*",
        "../../core/src/*"
      ],
    },
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "next-env.d.ts",
    "global.d.ts",
    "next.config.js",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}