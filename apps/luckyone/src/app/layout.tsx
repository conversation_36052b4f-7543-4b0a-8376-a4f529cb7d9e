import type { Metadata } from 'next'
import { Providers } from '@/providers'
import { PrimaryFont, SecondaryFont } from '@app/layout.settings'
import { envVars } from '@core/env'
import '@/app/globals.scss'

if (process.env.USE_MSW === 'true') {
  const msw = require('@/mock/server/server').default
  msw.startMSWServer()
}

export const metadata: Metadata = {
  title: envVars.NEXT_PUBLIC_APP_NAME,
  description: 'Generated by create next app',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${PrimaryFont.className} ${SecondaryFont.className}`} suppressHydrationWarning>
      <body suppressHydrationWarning>
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
