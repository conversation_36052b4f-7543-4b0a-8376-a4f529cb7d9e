import { Funnel_Sans } from 'next/font/google'
import localFont from 'next/font/local'

export const SecondaryFont = Funnel_Sans({
  subsets: ['latin'],
})

const Gilroy = localFont({
  src: [
    {
      path: '../../assets/fonts/<PERSON><PERSON>/<PERSON><PERSON>-UltraLight.ttf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../../assets/fonts/Gilroy/<PERSON>roy-Light.ttf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../../assets/fonts/<PERSON><PERSON>/<PERSON>roy-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../assets/fonts/<PERSON><PERSON>/<PERSON>roy-RegularItalic.ttf',
      weight: '400',
      style: 'italic',
    },
    {
      path: '../../assets/fonts/<PERSON>roy/Gilroy-Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../assets/fonts/<PERSON><PERSON>/<PERSON>-SemiBold.ttf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../../assets/fonts/<PERSON><PERSON>/<PERSON>-Bold.ttf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../../assets/fonts/<PERSON>roy/Gilroy-ExtraBold.ttf',
      weight: '800',
      style: 'normal',
    },
    {
      path: '../../assets/fonts/Gilroy/Gilroy-Black.ttf',
      weight: '900',
      style: 'normal',
    },
  ],
})

export { Gilroy as PrimaryFont }
