// This file was auto-generated by generate-heroui-config.mjs
// Do not edit directly - run "yarn generate-heroui-config" to regenerate

export default {
  themes: {
    light: {
      colors: {
        primary: {
          50: '#fff7e5',
          100: '#ffe5b3',
          200: '#ffd480',
          300: '#ffc34d',
          400: '#ffb31a',
          500: '#ffaa00',
          600: '#e69900',
          700: '#b37700',
          800: '#805500',
          900: '#4d3300',
          DEFAULT: '#ffaa00',
          foreground: '#08082a',
        },
        secondary: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#ffffff',
          300: '#ffffff',
          400: '#ffffff',
          500: '#ffffff',
          600: '#f2f2f2',
          700: '#d9d9d9',
          800: '#bfbfbf',
          900: '#a6a6a6',
          DEFAULT: '#ffffff',
          foreground: '#1e1c38',
        },
        success: {
          50: '#f2faea',
          100: '#d9f1c1',
          200: '#c0e797',
          300: '#a7de6d',
          400: '#8ed544',
          500: '#82d02f',
          600: '#75bb2a',
          700: '#5b9221',
          800: '#416818',
          900: '#273e0e',
          DEFAULT: '#82d02f',
          foreground: '#ffffff',
        },
        danger: {
          50: '#f9e9e9',
          100: '#eec1c1',
          200: '#e39999',
          300: '#d87171',
          400: '#ce4949',
          500: '#c73636',
          600: '#b33131',
          700: '#8b2626',
          800: '#631b1b',
          900: '#3b1010',
          DEFAULT: '#c73636',
          foreground: '#000000',
        },
        warning: {
          50: '#f9e9e9',
          100: '#eec1c1',
          200: '#e39999',
          300: '#d87171',
          400: '#ce4949',
          500: '#c73636',
          600: '#b33131',
          700: '#8b2626',
          800: '#631b1b',
          900: '#3b1010',
          DEFAULT: '#c73636',
          foreground: '#000000',
        },
        background: '#e8e7f3',
        foreground: '#9997b1',
        focus: '#e1a123',
        overlay: '#9997b1',
        default: {
          50: '#817cbd',
          100: '#605aac',
          200: '#4c478d',
          300: '#39366b',
          400: '#272549',
          500: '#1e1c38',
          600: '#151327',
          700: '#030205',
          800: '#000000',
          900: '#000000',
          DEFAULT: '#1e1c38',
          foreground: '#fafaff',
        },
        content1: {
          DEFAULT: '#ffaa00',
          foreground: '#08082a',
        },
        content2: {
          DEFAULT: '#ffffff',
          foreground: '#1e1c38',
        },
        content3: {
          DEFAULT: '#741CC2',
          foreground: '#ffffff',
        },
        content4: {
          DEFAULT: '#741CC2',
          foreground: '#ffffff',
        },
      },
    },
    dark: {
      colors: {
        primary: {
          50: '#ffffff',
          100: '#fff6d6',
          200: '#ffeaa3',
          300: '#ffde70',
          400: '#ffd23d',
          500: '#ffcc24',
          600: '#ffc60a',
          700: '#d6a500',
          800: '#a37d00',
          900: '#705600',
          DEFAULT: '#ffcc24',
          foreground: '#08082a',
        },
        secondary: {
          50: '#9a9aa1',
          100: '#7f7f88',
          200: '#66666e',
          300: '#4e4e54',
          400: '#353539',
          500: '#29292c',
          600: '#1d1d1f',
          700: '#040404',
          800: '#000000',
          900: '#000000',
          DEFAULT: '#29292c',
          foreground: '#ffffff',
        },
        success: {
          50: '#f2faea',
          100: '#d9f1c1',
          200: '#c0e797',
          300: '#a7de6d',
          400: '#8ed544',
          500: '#82d02f',
          600: '#75bb2a',
          700: '#5b9221',
          800: '#416818',
          900: '#273e0e',
          DEFAULT: '#82d02f',
          foreground: '#ffffff',
        },
        danger: {
          50: '#f9e9e9',
          100: '#eec1c1',
          200: '#e39999',
          300: '#d87171',
          400: '#ce4949',
          500: '#c73636',
          600: '#b33131',
          700: '#8b2626',
          800: '#631b1b',
          900: '#3b1010',
          DEFAULT: '#c73636',
          foreground: '#000000',
        },
        warning: {
          50: '#f9e9e9',
          100: '#eec1c1',
          200: '#e39999',
          300: '#d87171',
          400: '#ce4949',
          500: '#c73636',
          600: '#b33131',
          700: '#8b2626',
          800: '#631b1b',
          900: '#3b1010',
          DEFAULT: '#c73636',
          foreground: '#000000',
        },
        background: '#111113',
        foreground: '#3b3b3e',
        focus: '#ffc400',
        overlay: '#3b3b3e',
        default: {
          50: '#ffffff',
          100: '#ffffff',
          200: '#ffffff',
          300: '#ffffff',
          400: '#ffffff',
          500: '#ffffff',
          600: '#f2f2f2',
          700: '#d9d9d9',
          800: '#bfbfbf',
          900: '#a6a6a6',
          DEFAULT: '#ffffff',
          foreground: '#161618',
        },
        content1: {
          DEFAULT: '#ffcc24',
          foreground: '#08082a',
        },
        content2: {
          DEFAULT: '#29292C',
          foreground: '#ffffff',
        },
        content3: {
          DEFAULT: '#741CC2',
          foreground: '#ffffff',
        },
        content4: {
          DEFAULT: '#741CC2',
          foreground: '#ffffff',
        },
      },
    },
  },
  layout: {
    radius: {
      small: '0.5rem',
      medium: '0.75rem',
      large: '1rem',
    },
    borderWidth: {
      small: '1px',
      medium: '1px',
      large: '1px',
    },
  },
}
