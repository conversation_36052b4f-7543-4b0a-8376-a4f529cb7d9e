{
  "extends": "../tsconfig.apps.json",
  "compilerOptions": {
    "plugins": [
      {
        "name": "next"
      },
      {
        "name": "typescript-plugin-css-modules",
        "options": {
          "classnameTransform": "camelCaseOnly"
        }
      }
    ],
    "baseUrl": "./src",
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "next-env.d.ts",
    "next.config.js",
    ".next/types/**/*.ts",
    "../core/src/**/*.ts",
    "../core/src/**/*.tsx"
  ],
  "exclude": [
    "node_modules"
  ]
}