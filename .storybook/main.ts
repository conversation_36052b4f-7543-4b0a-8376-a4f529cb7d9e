import type { StorybookConfig } from '@storybook/nextjs'
import type { WebpackConfiguration } from '@storybook/core-webpack'
import path, { join, dirname } from 'path'

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): any {
  return dirname(require.resolve(join(value, 'package.json')))
}
const config: StorybookConfig = {
  addons: [getAbsolutePath('@storybook/addon-onboarding')],
  framework: {
    name: getAbsolutePath('@storybook/nextjs'),
    options: {},
  },
  stories: [
    '../apps/core/src/components/**/*.stories.@(js|jsx|ts|tsx|mdx)',
  ],
  webpackFinal: async (config: WebpackConfiguration) => {
    config.resolve = config.resolve || {}

    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@components': path.resolve(__dirname, '../apps/core/src/components'),
      '@modules': path.resolve(__dirname, '../apps/core/src/modules'),
      '@theme': path.resolve(__dirname, '../apps/core/src/theme'),
      '@app': path.resolve(__dirname, '../apps/core/src/app'),
    }
    return config
  },
}
export default config
