{"extends": "@repo/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@app/*": ["../apps/core/src/app/*"], "@screens/*": ["../apps/core/src/components/screens/*"], "@components/*": ["../apps/core/src/components/*"], "@modules/*": ["../apps/core/src/modules/*"], "@theme/*": ["../apps/core/src/theme/*"], "@/*": ["../apps/core/src/*"]}}, "include": ["../apps/core/src/**/*", "../apps/core/**/*.stories.*", "../apps/core/**/*.story.*", "./*.ts", "./*.tsx"], "exclude": ["../node_modules", "../apps/*/node_modules"]}