# RFC: Layout Configuration System

## 📋 **Overview**

This RFC proposes a unified layout configuration system that can be reused across all layout components (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bar, etc.) in our application.

## 🎯 **Goals**

1. **Consistency**: Standardize how we configure layouts across the app
2. **Reusability**: Create patterns that work for <PERSON><PERSON>, <PERSON><PERSON>, Sidebar, etc.
3. **Flexibility**: Support responsive design and A/B testing
4. **Maintainability**: Type-safe configurations that are easy to validate
5. **Business Control**: Allow non-technical teams to modify layouts

## 🏗️ **Proposed Architecture**

### **Core Interface**

```typescript
interface LayoutConfig {
  id: string
  name: string
  version: string
  market: string
  lastUpdated: string
  metadata?: {
    description?: string
    author?: string
    tags?: string[]
  }
  layout: {
    type: 'grid' | 'flex' | 'stack'
    breakpoints?: { mobile: number; tablet: number; desktop: number }
    spacing?: { padding?: string; margin?: string; gap?: string }
  }
  sections: LayoutSection[]
}

interface LayoutSection {
  id: string
  type: string
  enabled: boolean
  order: number
  position?: 'left' | 'center' | 'right' | 'top' | 'bottom' | 'full-width'
  responsive?: {
    mobile?: Partial<LayoutSection>
    tablet?: Partial<LayoutSection>
    desktop?: Partial<LayoutSection>
  }
  config: Record<string, any>
}
```

## 🔍 **Key Features**

### **1. Responsive Design**
```json
{
  "position": "left",
  "responsive": {
    "mobile": { "position": "top" },
    "tablet": { "position": "center" }
  }
}
```

### **2. A/B Testing Support**
```json
{
  "enabled": true,
  "order": 1
}
```

### **3. Type Safety**
- Specific interfaces for each layout type (Footer, Header, etc.)
- Validation service with error reporting
- Type guards for section configurations

### **4. Business Configurability**
- JSON-based configuration
- Clear section IDs and types
- Enable/disable functionality

## 🎨 **Footer Implementation Example**

```json
{
  "id": "footer-layout-en-row",
  "name": "Footer Layout - English ROW",
  "version": "1.0.0",
  "sections": [
    {
      "id": "brand-section",
      "type": "brand",
      "enabled": true,
      "order": 1,
      "position": "left",
      "config": {
        "logo": {
          "type": "text",
          "text": "LUCKY ONE",
          "superscript": "US"
        }
      }
    }
  ]
}
```

## 🔧 **Service Layer**

```typescript
class LayoutConfigService {
  static getFooterLayoutConfig(market: string): Promise<FooterLayoutConfig>
  static getEnabledSections(config: LayoutConfig): LayoutSection[]
  static getSectionsByPosition(config: LayoutConfig, position: string): LayoutSection[]
  static validateConfig(config: LayoutConfig): LayoutConfigValidation
}
```

## 🚀 **Benefits**

### **For Developers**
- ✅ Consistent patterns across all layouts
- ✅ Type safety and validation
- ✅ Easy to test and maintain
- ✅ Responsive design built-in

### **For Business Teams**
- ✅ Configure layouts without code changes
- ✅ A/B testing capabilities
- ✅ Market-specific configurations
- ✅ Easy enable/disable of features

### **For Design System**
- ✅ Standardized layout patterns
- ✅ Consistent spacing and breakpoints
- ✅ Reusable component configurations

## 🤔 **Questions for Discussion**

1. **Scope**: Should we start with Footer only, or design for all layouts from the beginning?

2. **Configuration Complexity**: Is the proposed structure too complex, or does it provide the right level of flexibility?

3. **Responsive Design**: Are the responsive overrides sufficient, or do we need more granular control?

4. **Validation**: What level of validation should we implement? Runtime vs build-time?

5. **Migration**: How should we migrate existing components to this new system?

6. **Performance**: Any concerns about the configuration size or parsing performance?

7. **Developer Experience**: Does this make it easier or harder for developers to work with layouts?

## 📝 **Next Steps**

1. **Team Review**: Get feedback on the proposed architecture
2. **Prototype**: Implement Footer layout configuration as proof of concept
3. **Validation**: Test with real business requirements
4. **Documentation**: Create comprehensive docs and examples
5. **Migration Plan**: Define how to migrate existing components

## 🔗 **Related Files**

- `packages/types/api/s3/layout-config.d.ts` - Type definitions
- `packages/api/data/s3/configs/footer-layout-config_en_ROW.json` - Example config
- `apps/core/src/services/LayoutConfig.service.ts` - Service implementation

---

**Please review and provide feedback on:**
- Architecture design
- Configuration structure
- Service API
- Migration strategy
- Any missing requirements
