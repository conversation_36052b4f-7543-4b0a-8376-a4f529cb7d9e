{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "test": {"dependsOn": ["^build"]}, "dev": {"cache": false, "persistent": true}, "dev:msw": {"cache": false, "persistent": true}, "clear-cache": {"cache": false, "persistent": true}}, "globalEnv": ["NEXT_PUBLIC_APP_NAME", "SUPPORTED_MARKETS", "NODE_ENV", "USE_MSW", "AUTH_SECRET", "NEXT_PUBLIC_BACKEND_URL", "NEXT_PUBLIC_S3_URL", "DEFAULT_MARKET", "VERBOSE_LOGGING"], "globalDependencies": [".env", "tsconfig.json"]}