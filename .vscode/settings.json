{"createCSSModules.path": ".", "createCSSModules.extension": "scss", "createCSSModules.autoImport": true, "createCSSModules.identifier": "styles", "typescript.tsdk": "node_modules/typescript/lib", "javascript.preferences.importModuleSpecifier": "non-relative", "typescript.preferences.importModuleSpecifier": "non-relative", "folder-color.pathColors": [{"folderPath": "rhino-next/apps/luckyone/", "badge": "L1"}, {"folderPath": "rhino-next/apps/luckytwo/", "badge": "L2"}], "workbench.colorCustomizations": {}, "stylelint.validate": ["css", "scss"], "files.associations": {"globals.css": "tailwindcss"}, "editor.codeActionsOnSave": {"source.fixAll.markdownlint": "explicit", "source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "jest.jestCommandLine": "yarn test -- ", "jest.runMode": "on-demand", "emmet.triggerExpansionOnTab": true, "emmet.showSuggestionsAsSnippets": true, "editor.snippetSuggestions": "top", "duplicatedCode.options": {"minTokens": 90, "minLines": 10, "maxLines": 1000, "absolute": true, "silent": true, "gitignore": true, "noSymlinks": false, "pattern": "**/src/**/*.{ts,tsx}"}, "duplicatedCode.exclude": ["**/(node_modules|coverage|dist|build|vendor|storage|public|stubs|.git)/**", "**/.*", "**/_*", "**/*lock*", "**/LICENSE", "**/*.(test|d|api|stories).*", "**/(node_modules|theme)/**"]}