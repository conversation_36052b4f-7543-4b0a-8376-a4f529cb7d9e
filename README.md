# Rhino Next Development Guide

Rhino Next is a **monorepo** project built with **Next.js** and **Turborepo**. It includes multiple applications and shared packages to streamline development and deployment across different sites while maintaining shared conventions and tooling.

---

## 📋 Documentation Navigation

### 🚀 Getting Started

- [✨ Overview](#-overview)
- [🚀 Quick Setup](#-quick-setup)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Development](#development)
  - [Building](#building)
  - [VS Code Integration](#vs-code-integration)
- [🔧 Mock API Setup (Mockoon)](#-mock-api-setup-mockoon)

### 🏗️ Project Architecture

- [🏗️ Project Structure](#️-project-structure)
- [📁 Component File Structure Convention](#-component-file-structure-convention)
- [📱 Multi-Site Architecture](#-multi-site-architecture)

### 🎨 Styling & Design

- [🎨 Styling Conventions](#-styling-conventions)

### ⚛️ Development

- [⚛️ Component Development](#️-component-development)
  (#client-component-template)
- [🎯 Architecture Patterns](#-architecture-patterns)
  - [Component Organization](#component-organization)
  - [State Management](#state-management)
  - [Data Fetching](#data-fetching)

### 🔧 Tools & Development Workflow

- [🔧 Development Tools](#-development-tools)
  - [Component Creation Script](#component-creation-script)
  - [VS Code Tasks](#vs-code-tasks)
  - [Style Dictionary](#style-dictionary)
  - [Recommended Extensions](#recommended-extensions)
- [🚀 Additional Development Commands](#-additional-development-commands)
  - [Advanced Development](#advanced-development)
  - [Building & Testing](#building--testing)
  - [Linting & Formatting](#linting--formatting)
  - [Storybook](#storybook)

### 🔍 Quality & Testing

- [🔍 Code Quality](#-code-quality)
- [🧪 Testing](#-testing)
- [🔄 Development Workflow](#-development-workflow)

### 📚 Resources & Help

- [📚 Additional Resources](#-additional-resources)
- [📚 GitHub Slack Integration](#-github-slack-integration)
- [🆘 Common Issues & Troubleshooting](#-common-issues--troubleshooting)
  - [Setup Issues](#setup-issues)
  - [Styling Issues](#styling-issues)
  - [Import Issues](#import-issues)
  - [Build Issues](#build-issues)
  - [Mock API Issues](#mock-api-issues)
- [🎉 You're Ready to Go!](#-youre-ready-to-go)

### 🔗 Quick Links

- **Core App**: [apps/core](./apps/core/) - Main application
- **LuckyOne App**: [apps/luckyone](./apps/luckyone/) - Site-specific app
- **LuckyTwo App**: [apps/luckytwo](./apps/luckytwo/) - Site-specific app
- **API Package**: [packages/api](./packages/api/) - API mocks and endpoints
- **UI Components**: [packages/ui](./packages/ui/) - Shared UI components
- **Types**: [packages/types](./packages/types/) - TypeScript definitions
- **Constants**: [packages/constants](./packages/constants/) - Shared constants
- **Helpers**: [packages/helpers](./packages/helpers/) - Utility functions
- **Hooks**: [packages/hooks](./packages/hooks/) - Shared React hooks
- **Scripts**: [scripts](./scripts/) - Build and development scripts
- **CSS Units Guide**: [apps/core/src/theme/CSS_UNITS_GUIDE.md](./apps/core/src/theme/CSS_UNITS_GUIDE.md)

### 🛠️ Development Commands Quick Reference

```bash
# Development
yarn dev                    # Start all apps
yarn dev:core              # Start core app only
yarn dev:luckyone          # Start luckyone app only
yarn dev:luckytwo          # Start luckytwo app only
yarn dev:msw               # Start with Mock Service Worker

# Building
yarn build                 # Build all projects
yarn test                  # Run all tests
yarn lint                  # Lint all projects
yarn format                # Format code

# Component Creation
yarn create-component      # Interactive component generator
turbo gen                  # Create pages with generator

# Storybook
yarn storybook:core        # Start core Storybook
yarn storybook:all         # Start all Storybooks

# Style Dictionary
yarn build-style-dictionary # Generate design tokens
```

---

## ✨ Overview

This guide provides comprehensive documentation for developing in the Rhino Next monorepo. After completing the setup above, use this guide to understand our conventions, tooling, and best practices for consistent development across the team.

## 🚀 Quick Setup

### Prerequisites

Make sure you have Node.js (v20+).

### VSCode Recommended Extensions

```bash
# Install all recommended VS Code extensions
yarn install-recommended-extensions
```

### Installation

Setup yarn and install dependencies:

```bash
corepack enable
yarn install
```

### Development

Run the development server:

```bash
yarn dev
```

Run with Mock Service Worker (MSW) for API mocking:

```bash
yarn api:start
```

Or open the [Mockoon](#-mock-api-setup-mockoon) app and start the environments

### Building

Build all projects:

```bash
yarn build
```

Build specific app:

```bash
cd apps/<app-name>
yarn build
```

### VS Code Integration

If you use VS Code, you can use **Run and Debug** to run the project with pre-configured tasks.

![VS Code Debug](assets/image.png)

## 🔧 Mock API Setup (Mockoon)

To run the project with mock APIs, you need to install and configure **Mockoon**:

1. **Install Mockoon**  
   Download and install Mockoon from their official site: [https://mockoon.com](https://mockoon.com)

2. **Import Mock API Environments**  
   Open the Mockoon app, then:

   - Click on **"Open local environment"**
   - Navigate to the project directory
   - Select and open the environment files located at:

     ```
     /packages/api/mockoon
     ```

3. **Start the Mockoon environment**  
   Make sure the environment is running before starting the dev server with mock support.

   <img src="assets/image-1.png" alt="Mockoon Environments" width="300" />

## Insomnia Setup (Optional)

If you prefer using Insomnia for API testing, you can import the provided Insomnia workspace:

1. **Install Insomnia**  
   Download and install Insomnia from their official site: [https://insomnia.rest](https://insomnia.rest)
2. **Import Insomnia Workspace**
   Open Insomnia, then:
   - Click on **"Import/Export"**
   - Select **"Import Data"**
   - Choose the `insomnia.json` file located in the `packages/api/insomnia` directory

## 🏗️ Project Structure

Rhino Next is a **monorepo** built with **Next.js** and **Turborepo** that includes multiple applications and shared packages.

### Workspace Structure

```
rhino-next/
├── apps/
│   ├── core/                 # Main application
│   ├── luckyone/                # Site-specific app (extends core)
│   └── luckytwo/                # Site-specific app (extends core)
├── packages/
│   ├── api/                  # API mocks and endpoints
│   ├── constants/            # Shared constants
│   ├── helpers/              # Utility functions
│   ├── hooks/                # Shared React hooks
│   ├── types/                # TypeScript definitions
│   └── ui/                   # Shared UI components
└── scripts/                  # Build and development scripts
```

## 📁 Component File Structure Convention

Every component follows this **strict naming convention**:

```
MyComponent/
├── MyComponent.tsx              # Main component (Server Component by default or Client Component if it is a standalone)
├── MyComponent.client.tsx       # Client Component (if needed)
├── MyComponent.module.scss      # Component styles
├── MyComponent.stories.tsx      # Storybook stories (root components only)
├── MyComponent.settings.ts      # Component configuration (if needed)
└── MyComponent.sd.module.scss # Style Dictionary variables
```

> 💡 **Quick Start**: Use the automated component generator to create components following these conventions:
>
> ```bash
> yarn create-component
> # or use VS Code Command Palette: "Tasks: Run Task" → "Create Component"
> ```

### Nested Components Pattern

For complex components that need sub-components, you can create a **`components/` subfolder** within the main component directory:

```
Sidebar/
├── Sidebar.tsx
├── Sidebar.module.scss
├── SidebarBase.client.tsx
├── SidebarContentComponents.ts
├── _sidebar.sd.module.scss
├── components/                    # Nested components subfolder
│   ├── Chat/
│   │   ├── Chat.tsx
│   │   ├── Chat.client.tsx
│   │   └── ChatData.tsx
│   ├── MainNavigation/
│   │   └── MainNavigation.tsx
│   ├── SidebarDivider/
│   │   ├── SidebarDivider.tsx
│   │   └── SidebarDivider.module.scss
│   └── shared/                    # Shared utilities for nested components
│       ├── FlexItemsWrapper.tsx
│       └── SidebarStates.tsx
└── utils/                         # Component utilities
```

#### When to Use Nested Components

- **Complex components** with multiple sub-components (like Sidebar, ImgIx)
- **Logically grouped** functionality that belongs together
- **Reusable sub-components** that are only used within the parent

#### Import Patterns for Nested Components

```tsx
// Import nested components using full path aliases
import { Chat } from '@components/Sidebar/components/Chat/Chat'
import { MainNavigation } from '@components/Sidebar/components/MainNavigation/MainNavigation'
import { ImgIxWithResize } from '@components/ImgIx/components/ImgIxWithResize/ImgIxWithResize'
```

#### Nested Component File Structure

Each nested component follows the same naming conventions as root components:

```
components/MyNestedComponent/
├── MyNestedComponent.tsx
├── MyNestedComponent.client.tsx     # If needed
├── MyNestedComponent.module.scss    # If needed
└── MyNestedComponent.sd.module.scss # If needed
```

## 🎨 Styling Conventions

### SCSS File Structure

Every component has **two** SCSS files:

1. **Component styles** (`MyComponent.module.scss`):

```scss
@use './MyComponent.sd.module' as *; // some sub-components may not have a style-dictionary module generated
@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  background: $placeholder-variable;
  padding: calculate-rem(16px);
  border-radius: calculate-rem(8px);
}
```

2. **Style Dictionary variables** (`MyComponent.sd.module.scss`):

```scss
/**
 * Style Dictionary generated styles will go here
 */
@use '@theme/style-dictionary' as *;
$placeholder-variable: $color-primary;
```

### CSS Units Convention

Our project enforces **strict unit usage** via custom stylelint plugins:

#### For `rem` units (spacing, sizing)

```scss
// ✅ CORRECT - Use calculate-rem() for consistent scaling
.container {
  padding: calculate-rem(16px);
  margin: calculate-rem(24px);
  font-size: calculate-rem(18px);
  border-radius: calculate-rem(8px);
}

// ❌ WRONG - Raw px/rem values
.container {
  padding: 16px; // Will be auto-fixed to calculate-rem(16px)
  margin: 1.5rem; // Will be auto-fixed to calculate-rem(24px)
}
```

#### For `px` units (borders, shadows)

```scss
// ✅ CORRECT - Use px for crisp rendering
.container {
  border: 1px solid $color-border;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
```

#### For unitless values

```scss
// ✅ CORRECT - Unitless for proportional scaling
.text {
  line-height: 1.5;
  z-index: 10;
}
```

### Required Imports

All SCSS files **must** include the theme import:

```scss
@use '@theme/variables.scss' as *;
```

This is automatically checked and added by our stylelint plugins.

## ⚛️ Component Development

### Server vs Client Components

- **Default**: Components are **Server Components**
- **Use `.client.tsx`** when you need:
  - React hooks (`useState`, `useEffect`, etc.)
  - Browser APIs
  - Event handlers
  - Context providers/consumers

### Component Template

```tsx
import type { FC } from 'react'
import styles from '@components/MyComponent/MyComponent.module.scss'

interface IMyComponentProps {
  children?: React.ReactNode
  className?: string
  variant?: 'primary' | 'secondary'
}

export const MyComponent: FC<IMyComponentProps> = ({ children, className, variant = 'primary', ...props }) => {
  return (
    <div className={`${styles.container} ${className || ''}`} {...props}>
      {children}
    </div>
  )
}
```

### Client Component Template

```tsx
'use client'
import { useState } from 'react'
import type { FC } from 'react'
import styles from '@components/MyComponent/MyComponent.module.scss'

interface IMyComponentProps {
  onAction?: (value: string) => void
}

export const MyComponent: FC<IMyComponentProps> = ({ onAction }) => {
  const [state, setState] = useState('')

  return <div className={styles.container}>{/* Interactive content */}</div>
}
```

## 🎯 Architecture Patterns

### Component Organization

```
components/
├── ui/
│   └── shadcn/          # Shared UI primitives
├── screens/             # Page-level components
├── shared/              # Reusable components
└── [feature]/           # Feature-specific components
    ├── components/      # Sub-components
    └── utils/           # Feature utilities
```

### State Management

- **Client state**: Zustand stores
- **Server state**: React Server Components + `unstable_cache`
- **Forms**: React Hook Form + Zod validation

### Data Fetching

- **Server Components**: Direct API calls
- **Client Components**: Custom hooks with SWR/React Query patterns

## 🔧 Development Tools

### Component Creation Script

#### 🚀 Automatically create pages following our conventions

**✅ Step 1: Ensure Turbo is Installed Globally**

Before running the generator command, make sure turbo is installed globally on your machine.

Check if Turbo is installed:

```bash
npx turbo --version
```

**🛠️ Step 2: Run the Page Generator**

Once Turbo is installed, run the generator:

```bash
# Run the pages generator
turbo gen
```

**📋 What Happens Next**

After running the command, you’ll be prompted to:

1. Enter the name of the page
2. Choose the location for the new page
3. Specify whether it's for authenticated users or globally available

#### 🚀 Automatically create components following our conventions

Run the component generator

```bash
yarn create-component
```

or

```bash
node ./scripts/create-component.mjs
```

**Example workflow:**

1. Enter component name: `MyAwesomeComponent`
2. Choose directory (or use default): `components/shared/`
3. Script creates:
   - `MyAwesomeComponent.tsx`
   - `MyAwesomeComponent.module.scss`
   - `MyAwesomeComponent.sd.module.scss`
   - `MyAwesomeComponent.stories.tsx` (if root component)

### VS Code Tasks

Use VS Code's **Run and Debug** or Command Palette (`Cmd+Shift+P`):

- **Reexport core styles**: Syncs styles between core and sites
- **Create Component**: Interactive component generator

### Style Dictionary

```bash
# Generate design tokens
yarn build-sd

# Generate TypeScript objects from tokens
yarn generate-sd-objects
```

## 🚀 Additional Development Commands

### Advanced Development

```bash
# Start specific app
yarn workspace core dev
yarn workspace luckyone dev
yarn workspace luckytwo dev
```

### Building & Testing

```bash
# Run all tests
yarn test

# Run tests for core app
yarn test:core
```

### Linting & Formatting

```bash
# Lint all projects
yarn lint

# Format code
yarn format

# Type checking
yarn check-types
```

### Storybook

```bash
# Start Storybook for core
yarn storybook:core

# Start all Storybooks
yarn storybook:all
```

## 🔍 Code Quality

### Automatic Fixes

The project includes **auto-fix** functionality:

- **CSS units**: Automatically converts `px`/`rem` to `calculate-rem()`
- **Missing imports**: Adds required SCSS imports
- **VS Code**: Enable `"source.fixAll.stylelint": "explicit"` for auto-fix on save

### ESLint Rules

- Path aliases enforced
- Unused imports removed
- Prettier integration
- React/Next.js best practices

### Stylelint Rules

- Unit enforcement
- Color variable usage
- Import requirements
- Browser compatibility warnings

## 🧪 Testing

### Test Structure

```
__tests__/
├── jest.setup.ts        # Global test setup
├── jest.setupAfterEnv.ts # After env setup
└── [component].test.tsx  # Component tests
```

### Testing Utilities

- **Jest**: Test runner
- **React Testing Library**: Component testing
- **MSW**: API mocking

## 🔄 Development Workflow

### 1. Create Feature Branch

```bash
git checkout -b feature/my-awesome-feature
```

### 2. Create Components

```bash
yarn create-component
```

### 3. Develop with Auto-fixing

- Save files to auto-fix styling issues
- Use Storybook for isolated development

### 4. Test & Lint

```bash
yarn test
yarn lint
yarn check-types
```

### 5. Build & Verify

```bash
yarn build
```

## 📚 Additional Resources

- **Turborepo**: [https://turbo.build/](https://turbo.build/)
- **Next.js**: [https://nextjs.org/](https://nextjs.org/)
- **HeroUI**: [https://heroui.com/](https://heroui.com/)
- **Style Dictionary**: [https://amzn.github.io/style-dictionary/](https://amzn.github.io/style-dictionary/)
- **Mockoon**: [https://mockoon.com](https://mockoon.com)

## 🤖 GitHub Slack Integration

To stay updated with repository activity, you can subscribe to GitHub notifications in Slack using the GitHub Slack bot.

### Setup GitHub Slack Bot

1. Visit the [GitHub Slack App](https://slack.github.com/) and install it to your Slack workspace
2. In your Slack channel, use the following command to subscribe to repository notifications:

```bash
/github subscribe rhinoent/rhino-next issues pulls commits releases deployments comments:"channel" reviews:"channel"
```

For more configuration options, visit the [GitHub Slack documentation](https://slack.github.com/).

## 🆘 Common Issues & Troubleshooting

### Setup Issues

- **Problem**: Yarn not found
- **Solution**: Run `corepack enable` to activate yarn

### Styling Issues

- **Problem**: Units not being converted
- **Solution**: Ensure `@use '@theme/variables.scss' as *;` is imported

### Import Issues

- **Problem**: Module not found
- **Solution**: Check path aliases in `tsconfig.json`

### Build Issues

- **Problem**: Type errors
- **Solution**: Run `yarn check-types` and fix TypeScript issues

### Mock API Issues

- **Problem**: API calls failing
- **Solution**: Ensure Mockoon environment is running before starting dev server

---

## 🎉 You're Ready to Go

With this setup and guide, you have everything needed to develop effectively in the Rhino Next monorepo. The conventions and tools documented here ensure consistent, high-quality code across all team members.

**Next Steps:**

1. ✅ Complete the setup above
2. 🏗️ Create your first component with `yarn create-component`
3. 🎨 Follow the styling conventions for consistent UI
4. 🧪 Write tests for your components
5. 🚀 Build amazing features!

Happy coding! 🚀
